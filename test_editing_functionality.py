#!/usr/bin/env python3
"""
測試剪輯功能的完整性
"""

import requests
import json
import time
import os
import sys

# 添加backend路徑到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_editing_api():
    """測試剪輯API的可用性"""
    base_url = "http://localhost:8000"
    
    print("🎬 測試剪輯功能可用性")
    print("=" * 50)
    
    # 1. 檢查API健康狀態
    print("1. 檢查API服務狀態...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("   ✅ API服務正常運行")
        else:
            print(f"   ❌ API服務異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 無法連接到API服務: {e}")
        return False
    
    # 2. 檢查項目列表
    print("2. 檢查項目列表...")
    try:
        response = requests.get(f"{base_url}/api/projects/")
        if response.status_code == 200:
            projects = response.json()
            print(f"   ✅ 找到 {len(projects)} 個項目")
            if len(projects) > 0:
                project_id = projects[0]['id']
                project_name = projects[0]['name']
                print(f"   📁 測試項目: {project_name} (ID: {project_id})")
            else:
                print("   ⚠️  沒有可用的項目進行測試")
                return False
        else:
            print(f"   ❌ 獲取項目列表失敗: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 獲取項目列表錯誤: {e}")
        return False
    
    # 3. 檢查項目文件
    print("3. 檢查項目文件...")
    try:
        response = requests.get(f"{base_url}/api/projects/{project_id}")
        if response.status_code == 200:
            project_detail = response.json()
            files = project_detail.get('files', [])
            print(f"   📄 項目包含 {len(files)} 個文件")
            
            video_files = [f for f in files if f.get('file_type') == 'video']
            audio_files = [f for f in files if f.get('file_type') == 'audio']
            subtitle_files = [f for f in files if f.get('file_type') == 'subtitle']
            
            print(f"   🎥 視頻文件: {len(video_files)} 個")
            print(f"   🎵 音頻文件: {len(audio_files)} 個")
            print(f"   📝 字幕文件: {len(subtitle_files)} 個")
            
            if len(video_files) == 0:
                print("   ⚠️  沒有視頻文件，無法進行剪輯測試")
                return False
            
            main_video = video_files[0]
            print(f"   🎬 主視頻: {main_video['filename']}")
            
        else:
            print(f"   ❌ 獲取項目詳情失敗: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 獲取項目詳情錯誤: {e}")
        return False
    
    # 4. 測試剪輯API端點
    print("4. 測試剪輯API端點...")
    try:
        # 測試編輯配置
        editing_config = {
            "project_id": project_id,
            "main_video_path": f"backend/storage/uploads/{project_id}/{main_video['filename']}",
            "subtitle_effect": "none",
            "background_volume": 0.3,
            "audio_volume": 1.0,
            "output_size": "original"
        }
        
        # 添加音頻和字幕文件（如果有的話）
        if audio_files:
            editing_config["background_music_path"] = f"backend/storage/uploads/{project_id}/{audio_files[0]['filename']}"
        
        if subtitle_files:
            editing_config["subtitle_path"] = f"backend/storage/uploads/{project_id}/{subtitle_files[0]['filename']}"
        
        print("   📋 剪輯配置:")
        for key, value in editing_config.items():
            if key != "project_id":
                print(f"      {key}: {value}")
        
        # 發送剪輯請求
        print("   🚀 發送剪輯請求...")
        response = requests.post(
            f"{base_url}/api/editing/start",
            json=editing_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            print(f"   ✅ 剪輯任務已創建: {job_id}")
            
            # 5. 監控任務進度
            print("5. 監控任務進度...")
            max_wait_time = 30  # 最多等待30秒
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                try:
                    progress_response = requests.get(f"{base_url}/api/progress/{job_id}")
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        status = progress_data.get('status', 'unknown')
                        progress = progress_data.get('progress', 0)
                        
                        print(f"   📊 任務狀態: {status}, 進度: {progress}%")
                        
                        if status == 'completed':
                            output_path = progress_data.get('output_path')
                            print(f"   🎉 剪輯完成! 輸出文件: {output_path}")
                            return True
                        elif status == 'failed':
                            error = progress_data.get('error', 'Unknown error')
                            print(f"   ❌ 剪輯失敗: {error}")
                            return False
                        elif status in ['pending', 'processing']:
                            time.sleep(2)  # 等待2秒後再檢查
                        else:
                            print(f"   ⚠️  未知狀態: {status}")
                            time.sleep(2)
                    else:
                        print(f"   ❌ 獲取進度失敗: {progress_response.status_code}")
                        break
                except Exception as e:
                    print(f"   ❌ 監控進度錯誤: {e}")
                    break
            
            print(f"   ⏰ 等待超時 ({max_wait_time}秒)，任務可能仍在進行中")
            print(f"   💡 請查看任務監控頁面: http://localhost:3001/tasks")
            return True
            
        else:
            print(f"   ❌ 創建剪輯任務失敗: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   📝 錯誤詳情: {error_detail}")
            except:
                print(f"   📝 錯誤內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 測試剪輯API錯誤: {e}")
        return False

def check_dependencies():
    """檢查剪輯功能的依賴項"""
    print("🔧 檢查剪輯功能依賴項")
    print("=" * 50)
    
    try:
        # 檢查moviepy
        import moviepy
        print(f"   ✅ MoviePy: {moviepy.__version__}")
    except ImportError:
        print("   ❌ MoviePy 未安裝")
        return False
    
    try:
        # 檢查huey
        import huey
        print(f"   ✅ Huey: {huey.__version__}")
    except ImportError:
        print("   ❌ Huey 未安裝")
        return False
    
    try:
        # 檢查ffmpeg
        from moviepy.config import check
        check()
        print("   ✅ FFmpeg: 可用")
    except Exception as e:
        print(f"   ⚠️  FFmpeg 檢查: {e}")
    
    return True

def main():
    """主測試函數"""
    print("🎬 自動視頻剪輯平台 - 剪輯功能測試")
    print("=" * 60)
    
    # 檢查依賴項
    if not check_dependencies():
        print("\n❌ 依賴項檢查失敗，請安裝必要的依賴項")
        return
    
    print()
    
    # 測試剪輯功能
    if test_editing_api():
        print("\n🎉 剪輯功能測試通過！")
        print("\n📋 可用功能:")
        print("   • 創建剪輯任務")
        print("   • 監控任務進度")
        print("   • 取消/重試任務")
        print("   • 下載剪輯結果")
        print("\n🌐 前端界面:")
        print("   • 項目詳情頁面: http://localhost:3001/projects/{project_id}")
        print("   • 任務監控頁面: http://localhost:3001/tasks")
        print("   • 文件上傳頁面: http://localhost:3001/upload")
    else:
        print("\n❌ 剪輯功能測試失敗")
        print("\n🔧 可能的解決方案:")
        print("   1. 確保後端API服務正在運行")
        print("   2. 確保Huey任務隊列服務正在運行")
        print("   3. 確保項目中有視頻文件")
        print("   4. 檢查文件路徑是否正確")

if __name__ == "__main__":
    main()
