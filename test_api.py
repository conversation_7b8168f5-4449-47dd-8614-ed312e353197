#!/usr/bin/env python3
"""Simple API test script"""

import sys
import os
import requests
import json

# Add backend to path
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

API_BASE = "http://localhost:8000/api"

def test_api():
    print("🔍 Testing API endpoints...")
    
    # Test health check
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    # Test create project
    try:
        project_data = {
            "name": "Test Project",
            "description": "Test Description",
            "status": "active"
        }
        response = requests.post(f"{API_BASE}/projects/", json=project_data)
        print(f"📋 Create project response: {response.status_code}")
        print(f"📋 Response content: {response.text}")
        
        if response.status_code == 200:
            project = response.json()
            print(f"✅ Project created successfully: {project}")
            return project
        else:
            print(f"❌ Project creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Project creation error: {e}")
        return None

if __name__ == "__main__":
    test_api()
