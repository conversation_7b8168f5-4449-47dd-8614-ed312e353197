@echo off
echo ========================================
echo Auto Video Editor - Simple Startup
echo ========================================
echo.

:: Check virtual environment
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found
    echo Please run setup_simple.bat first
    pause
    exit /b 1
)

echo Starting services...
echo.

:: Start backend
echo Starting backend service...
start "Backend API" cmd /k "venv\Scripts\activate.bat && cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Start task queue
echo Starting task queue...
start "Task Queue" cmd /k "venv\Scripts\activate.bat && cd backend && python -m huey.bin.huey_consumer app.tasks.editing_tasks.huey"

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Start frontend
echo Starting frontend service...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo SUCCESS: All services started!
echo.
echo Service URLs:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:8000
echo - API Docs: http://localhost:8000/docs
echo.
echo Press any key to open browser...
pause >nul

:: Open browser
start http://localhost:3000

echo.
echo Services are running in separate windows.
echo Close those windows to stop the services.
echo.
pause
