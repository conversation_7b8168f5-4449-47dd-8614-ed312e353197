import os
from typing import Optional, Dict, Any, List, Tuple
from moviepy.editor import AudioFileClip, CompositeAudioClip
import numpy as np
from app.core.config import settings

class AudioProcessor:
    """音頻處理服務"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.supported_formats = ['.wav', '.mp3', '.aac', '.flac', '.ogg', '.m4a']
    
    def process_audio(self, main_audio: AudioFileClip, config: Dict[str, Any]) -> AudioFileClip:
        """處理音頻的主函數"""
        try:
            processed_audio = main_audio
            
            # 處理背景音樂
            if config.get('background_music_path'):
                processed_audio = self.add_background_music(
                    processed_audio, 
                    config['background_music_path'],
                    config.get('background_volume', 0.3)
                )
            
            # 處理音頻替換
            if config.get('replace_audio_path'):
                processed_audio = self.replace_audio(
                    processed_audio,
                    config['replace_audio_path']
                )
            
            # 音量調整
            if config.get('audio_volume'):
                processed_audio = self.adjust_volume(
                    processed_audio,
                    config['audio_volume']
                )
            
            # 音頻淡入淡出
            if config.get('fade_in_duration'):
                processed_audio = processed_audio.audio_fadein(config['fade_in_duration'])
            
            if config.get('fade_out_duration'):
                processed_audio = processed_audio.audio_fadeout(config['fade_out_duration'])
            
            # 音頻靜音處理
            if config.get('mute_segments'):
                processed_audio = self.mute_segments(
                    processed_audio,
                    config['mute_segments']
                )
            
            return processed_audio
        
        except Exception as e:
            print(f"Error processing audio: {e}")
            raise
    
    def add_background_music(self, main_audio: AudioFileClip, 
                           music_path: str, volume: float = 0.3) -> AudioFileClip:
        """添加背景音樂"""
        try:
            if not os.path.exists(music_path):
                raise FileNotFoundError(f"Background music file not found: {music_path}")
            
            # 加載背景音樂
            background_music = AudioFileClip(music_path)
            
            # 調整背景音樂音量
            background_music = background_music.volumex(volume)
            
            # 如果背景音樂比主音頻短，循環播放
            if background_music.duration < main_audio.duration:
                # 使用audio_loop函數進行循環
                from moviepy.audio.fx.audio_loop import audio_loop
                background_music = audio_loop(background_music, duration=main_audio.duration)
            
            # 裁剪背景音樂到主音頻長度
            background_music = background_music.subclip(0, main_audio.duration)
            
            # 混合音頻
            composite_audio = CompositeAudioClip([main_audio, background_music])
            
            return composite_audio
        
        except Exception as e:
            print(f"Error adding background music: {e}")
            raise
    
    def replace_audio(self, original_audio: AudioFileClip, 
                     replacement_path: str) -> AudioFileClip:
        """替換音頻"""
        try:
            if not os.path.exists(replacement_path):
                raise FileNotFoundError(f"Replacement audio file not found: {replacement_path}")
            
            # 加載替換音頻
            replacement_audio = AudioFileClip(replacement_path)
            
            # 如果替換音頻比原音頻短，循環播放
            if replacement_audio.duration < original_audio.duration:
                # 使用audio_loop函數進行循環
                from moviepy.audio.fx.audio_loop import audio_loop
                replacement_audio = audio_loop(replacement_audio, duration=original_audio.duration)
            
            # 裁剪到原音頻長度
            replacement_audio = replacement_audio.subclip(0, original_audio.duration)
            
            return replacement_audio
        
        except Exception as e:
            print(f"Error replacing audio: {e}")
            raise
    
    def adjust_volume(self, audio: AudioFileClip, volume_factor: float) -> AudioFileClip:
        """調整音量"""
        try:
            if volume_factor <= 0:
                raise ValueError("Volume factor must be positive")
            
            return audio.volumex(volume_factor)
        
        except Exception as e:
            print(f"Error adjusting volume: {e}")
            raise
    
    def mute_segments(self, audio: AudioFileClip, 
                     segments: List[Tuple[float, float]]) -> AudioFileClip:
        """靜音指定時間段"""
        try:
            processed_audio = audio
            
            for start_time, end_time in segments:
                if start_time >= end_time:
                    continue
                
                if start_time < 0 or end_time > audio.duration:
                    continue
                
                # 創建靜音片段
                mute_clip = processed_audio.subclip(start_time, end_time).volumex(0)
                
                # 分割音頻並重新組合
                before_mute = processed_audio.subclip(0, start_time) if start_time > 0 else None
                after_mute = processed_audio.subclip(end_time) if end_time < audio.duration else None
                
                clips = []
                if before_mute:
                    clips.append(before_mute)
                clips.append(mute_clip)
                if after_mute:
                    clips.append(after_mute)
                
                if len(clips) > 1:
                    from moviepy.editor import concatenate_audioclips
                    processed_audio = concatenate_audioclips(clips)
            
            return processed_audio
        
        except Exception as e:
            print(f"Error muting segments: {e}")
            raise
    
    def normalize_audio(self, audio: AudioFileClip, target_level: float = -20.0) -> AudioFileClip:
        """音頻標準化"""
        try:
            # 獲取音頻數組
            audio_array = audio.to_soundarray()
            
            # 計算當前音量
            current_level = 20 * np.log10(np.sqrt(np.mean(audio_array**2)))
            
            # 計算需要調整的增益
            gain = target_level - current_level
            gain_factor = 10**(gain/20)
            
            # 應用增益
            return audio.volumex(gain_factor)
        
        except Exception as e:
            print(f"Error normalizing audio: {e}")
            return audio
    
    def apply_audio_effects(self, audio: AudioFileClip, 
                          effects: Dict[str, Any]) -> AudioFileClip:
        """應用音頻效果"""
        try:
            processed_audio = audio
            
            # 淡入效果
            if effects.get('fade_in'):
                duration = effects['fade_in'].get('duration', 1.0)
                processed_audio = processed_audio.audio_fadein(duration)
            
            # 淡出效果
            if effects.get('fade_out'):
                duration = effects['fade_out'].get('duration', 1.0)
                processed_audio = processed_audio.audio_fadeout(duration)
            
            # 音量包絡
            if effects.get('volume_envelope'):
                # 這裡可以實現更複雜的音量包絡效果
                pass
            
            # 音頻標準化
            if effects.get('normalize'):
                target_level = effects['normalize'].get('target_level', -20.0)
                processed_audio = self.normalize_audio(processed_audio, target_level)
            
            return processed_audio
        
        except Exception as e:
            print(f"Error applying audio effects: {e}")
            return audio
    
    def get_audio_info(self, audio_path: str) -> Dict[str, Any]:
        """獲取音頻文件信息"""
        try:
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
            audio = AudioFileClip(audio_path)
            
            info = {
                "format": os.path.splitext(audio_path)[1].lower(),
                "duration": audio.duration,
                "fps": audio.fps,
                "nchannels": audio.nchannels,
                "file_size": os.path.getsize(audio_path)
            }
            
            # 清理資源
            audio.close()
            
            return info
        
        except Exception as e:
            print(f"Error getting audio info: {e}")
            return {}
    
    def extract_audio_from_video(self, video_path: str) -> str:
        """從視頻中提取音頻"""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            
            from moviepy.editor import VideoFileClip
            
            # 加載視頻
            video = VideoFileClip(video_path)
            
            # 提取音頻
            audio = video.audio
            
            if audio is None:
                raise ValueError("Video file has no audio track")
            
            # 生成輸出路徑
            output_dir = os.path.join(settings.project_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)
            
            output_path = os.path.join(output_dir, "extracted_audio.wav")
            
            # 保存音頻
            audio.write_audiofile(output_path)
            
            # 清理資源
            audio.close()
            video.close()
            
            return output_path
        
        except Exception as e:
            print(f"Error extracting audio from video: {e}")
            raise
