@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 啟動器

echo ========================================
echo 🎬 自動視頻剪輯平台 - 一鍵啟動
echo ========================================
echo.

:: 檢查虛擬環境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虛擬環境不存在，請先運行 setup_windows.bat 進行環境設置
    pause
    exit /b 1
)

:: 檢查後端依賴
if not exist "backend\requirements.txt" (
    echo ❌ 後端配置文件不存在
    pause
    exit /b 1
)

:: 檢查前端依賴
if not exist "frontend\package.json" (
    echo ❌ 前端配置文件不存在
    pause
    exit /b 1
)

:: 檢查 node_modules
if not exist "frontend\node_modules" (
    echo ❌ 前端依賴未安裝，請先運行 setup_windows.bat
    pause
    exit /b 1
)

echo 🚀 正在啟動服務...
echo.

:: 激活虛擬環境
call venv\Scripts\activate.bat

:: 創建日誌目錄
if not exist "logs" mkdir logs

:: 啟動後端服務
echo 🔧 啟動後端服務...
start "後端服務" cmd /k "cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: 等待後端啟動
echo ⏳ 等待後端服務啟動...
timeout /t 3 /nobreak >nul

:: 啟動任務隊列
echo 🔧 啟動任務隊列...
start "任務隊列" cmd /k "cd backend && python -m huey_consumer app.tasks.editing_tasks.huey"

:: 等待任務隊列啟動
echo ⏳ 等待任務隊列啟動...
timeout /t 2 /nobreak >nul

:: 啟動前端服務
echo 🔧 啟動前端服務...
start "前端服務" cmd /k "cd frontend && npm run dev"

:: 等待前端啟動
echo ⏳ 等待前端服務啟動...
timeout /t 5 /nobreak >nul

:: 檢查服務狀態
echo 🔍 檢查服務狀態...

:: 檢查後端
echo 檢查後端服務...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 後端服務運行正常' } else { Write-Host '⚠️  後端服務可能未完全啟動' } } catch { Write-Host '⚠️  後端服務檢查失敗，可能仍在啟動中' }"

:: 等待一下再檢查前端
timeout /t 3 /nobreak >nul

:: 檢查前端
echo 檢查前端服務...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 前端服務運行正常' } else { Write-Host '⚠️  前端服務可能未完全啟動' } } catch { Write-Host '⚠️  前端服務檢查失敗，可能仍在啟動中' }"

echo.
echo 🎉 系統啟動完成！
echo.
echo 📋 服務信息：
echo - 前端界面: http://localhost:3000
echo - 後端 API: http://localhost:8000
echo - API 文檔: http://localhost:8000/docs
echo.
echo 💡 使用說明：
echo 1. 在瀏覽器中打開 http://localhost:3000 使用系統
echo 2. 查看 API 文檔: http://localhost:8000/docs
echo 3. 運行測試: test_windows.bat
echo 4. 停止服務: stop_windows.bat
echo.
echo 📝 注意事項：
echo - 請保持此窗口開啟以監控服務狀態
echo - 如需停止服務，請運行 stop_windows.bat
echo - 服務日誌會顯示在各自的命令行窗口中
echo.

:: 自動打開瀏覽器
set /p open_browser=是否自動打開瀏覽器？ (y/n): 
if /i "%open_browser%"=="y" (
    echo 🌐 正在打開瀏覽器...
    start http://localhost:3000
)

echo.
echo 按任意鍵退出啟動器（服務將繼續運行）...
pause >nul
