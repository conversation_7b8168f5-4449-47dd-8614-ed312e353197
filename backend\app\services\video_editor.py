from moviepy.editor import (
    VideoFileClip, AudioFileClip, CompositeVideoClip,
    concatenate_videoclips, TextClip, CompositeAudioClip
)
import os
import subprocess
from typing import Dict, Any, Optional, Callable, List
from app.services.subtitle_processor import SubtitleProcessor
from app.services.audio_processor import AudioProcessor
from app.core.config import settings

class VideoEditor:
    """視頻編輯服務"""

    def __init__(self, project_id: str, progress_callback: Optional[Callable] = None):
        self.project_id = project_id
        self.progress = 0
        self.progress_callback = progress_callback
        self.subtitle_processor = SubtitleProcessor(project_id)
        self.audio_processor = AudioProcessor(project_id)

    def update_progress(self, progress: int):
        """更新進度"""
        self.progress = progress
        if self.progress_callback:
            self.progress_callback(progress)

    def process_video(self, middle_video_paths: List[str], config: Dict[str, Any]) -> str:
        """處理視頻剪輯的主函數"""
        try:
            self.update_progress(5)

            # 加載並拼接片中視頻
            print(f"🎬 開始處理 {len(middle_video_paths)} 個視頻文件:")
            middle_clips = []
            total_duration = 0

            for i, video_path in enumerate(middle_video_paths):
                print(f"   📹 加載視頻 {i+1}: {video_path}")
                clip = VideoFileClip(video_path)
                duration = clip.duration
                size = clip.size
                total_duration += duration
                print(f"      尺寸: {size[0]}x{size[1]}, 時長: {duration:.2f}s")
                middle_clips.append(clip)

            print(f"📊 總共加載了 {len(middle_clips)} 個視頻片段，總時長: {total_duration:.2f}s")

            # 拼接片中視頻 - 修復: 使用 "chain" 而不是 "compose"
            if len(middle_clips) == 1:
                print("📝 只有一個視頻，直接使用")
                main_clip = middle_clips[0]
            else:
                print(f"🔗 拼接 {len(middle_clips)} 個視頻片段 (使用順序連接)")
                # 使用 "chain" 方法進行順序連接，而不是 "compose" 同時播放
                main_clip = concatenate_videoclips(middle_clips, method="chain")
                print(f"✅ 拼接完成，最終時長: {main_clip.duration:.2f}s")

            self.update_progress(15)

            # 處理音頻
            if config.get('audio_path') or config.get('background_music_path'):
                main_clip = self._process_audio(main_clip, config)
                self.update_progress(35)

            # 處理片頭片尾
            final_clip = self._add_intro_outro(main_clip, config)
            self.update_progress(45)

            # 處理視頻尺寸
            final_clip = self._process_video_size(final_clip, config)
            self.update_progress(55)

            # 生成輸出路徑
            output_dir = os.path.join(settings.output_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            temp_video_path = os.path.join(output_dir, "temp_video.mp4")
            final_output_path = os.path.join(output_dir, "final_video.mp4")

            # 先輸出不含字幕的視頻
            final_clip.write_videofile(
                temp_video_path,
                codec=config.get('video_codec', settings.default_video_codec),
                audio_codec=config.get('audio_codec', settings.default_audio_codec),
                bitrate=config.get('video_bitrate', settings.default_video_bitrate),
                verbose=False,
                logger=None
            )
            self.update_progress(75)

            # 處理字幕（使用ffmpeg嵌入）
            if config.get('subtitle_path'):
                subtitle_path = self._process_subtitles(config)
                final_output_path = self._embed_subtitles_with_ffmpeg(
                    temp_video_path, subtitle_path, final_output_path
                )
                # 刪除臨時文件
                if os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
            else:
                # 如果沒有字幕，直接重命名臨時文件
                # 如果目標文件已存在，先刪除
                if os.path.exists(final_output_path):
                    os.remove(final_output_path)
                os.rename(temp_video_path, final_output_path)

            self.update_progress(95)

            # 清理資源
            if 'middle_clips' in locals():
                for clip in middle_clips:
                    clip.close()
            main_clip.close()
            if 'final_clip' in locals():
                final_clip.close()

            self.update_progress(100)
            return final_output_path

        except Exception as e:
            print(f"Error processing video: {e}")
            # 清理可能的臨時文件
            if 'temp_video_path' in locals() and os.path.exists(temp_video_path):
                os.remove(temp_video_path)
            raise

    def _process_video_size(self, video_clip: VideoFileClip, config: Dict[str, Any]) -> VideoFileClip:
        """處理視頻尺寸調整"""
        try:
            output_size = config.get('output_size', 'original')

            # 如果是保持原始尺寸，直接返回
            if output_size == 'original':
                return video_clip

            # 解析尺寸字符串 (例如: "1920x1080")
            if 'x' in output_size:
                try:
                    width, height = map(int, output_size.split('x'))
                    print(f"Resizing video to {width}x{height}")

                    # 修復PIL.Image.ANTIALIAS問題的方法
                    # 先嘗試使用MoviePy的resize方法
                    try:
                        resized_clip = video_clip.resize((width, height))
                        print(f"Successfully resized video to {width}x{height}")
                        return resized_clip
                    except Exception as resize_error:
                        print(f"MoviePy resize failed: {resize_error}")

                        # 如果MoviePy resize失敗，嘗試使用FFmpeg直接處理
                        try:
                            print("Attempting FFmpeg-based resize...")
                            return self._ffmpeg_resize(video_clip, width, height)
                        except Exception as ffmpeg_error:
                            print(f"FFmpeg resize also failed: {ffmpeg_error}")
                            print("Using original size as fallback")
                            return video_clip

                except ValueError as e:
                    print(f"Invalid size format: {output_size}, using original size")
                    return video_clip
            else:
                print(f"Unknown size format: {output_size}, using original size")
                return video_clip

        except Exception as e:
            print(f"Error processing video size: {e}, using original size")
            return video_clip

    def _ffmpeg_resize(self, video_clip: VideoFileClip, width: int, height: int) -> VideoFileClip:
        """使用FFmpeg進行視頻尺寸調整的備用方法"""
        import tempfile
        import subprocess
        from moviepy.config import get_setting

        try:
            # 創建臨時文件
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_input:
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_output:
                temp_output_path = temp_output.name

            # 先將當前視頻寫入臨時文件
            print(f"Writing temporary input file: {temp_input_path}")
            video_clip.write_videofile(
                temp_input_path,
                verbose=False,
                logger=None
            )

            # 使用FFmpeg直接調整尺寸
            ffmpeg_cmd = [
                get_setting("FFMPEG_BINARY"),
                "-i", temp_input_path,
                "-vf", f"scale={width}:{height}",
                "-c:a", "copy",  # 保持音頻不變
                "-y",  # 覆蓋輸出文件
                temp_output_path
            ]

            print(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                check=True
            )

            # 加載調整後的視頻
            print(f"Loading resized video from: {temp_output_path}")
            resized_clip = VideoFileClip(temp_output_path)

            # 清理臨時文件
            try:
                import os
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
            except:
                pass  # 忽略清理錯誤

            print(f"Successfully resized video using FFmpeg to {width}x{height}")
            return resized_clip

        except subprocess.CalledProcessError as e:
            print(f"FFmpeg command failed: {e}")
            print(f"FFmpeg stderr: {e.stderr}")
            raise
        except Exception as e:
            print(f"FFmpeg resize method failed: {e}")
            raise

    def _process_audio(self, video_clip: VideoFileClip, config: Dict[str, Any]) -> VideoFileClip:
        """處理音頻"""
        try:
            # 獲取原始音頻
            original_audio = video_clip.audio

            if original_audio is None:
                # 如果視頻沒有音頻，創建靜音軌道
                from moviepy.editor import AudioClip
                original_audio = AudioClip(lambda t: [0, 0], duration=video_clip.duration)

            # 使用音頻處理器處理音頻
            processed_audio = self.audio_processor.process_audio(original_audio, config)

            # 將處理後的音頻設置到視頻
            return video_clip.set_audio(processed_audio)

        except Exception as e:
            print(f"Error processing audio: {e}")
            return video_clip

    def _process_subtitles(self, config: Dict[str, Any]) -> str:
        """處理字幕"""
        try:
            subtitle_path = config['subtitle_path']
            effect = config.get('subtitle_effect', 'none')

            # 使用字幕處理器處理字幕
            processed_subtitle_path = self.subtitle_processor.process_subtitle(
                subtitle_path, effect=effect, output_format="ass"
            )

            return processed_subtitle_path

        except Exception as e:
            print(f"Error processing subtitles: {e}")
            raise

    def _embed_subtitles_with_ffmpeg(self, video_path: str, subtitle_path: str,
                                   output_path: str) -> str:
        """使用ffmpeg嵌入字幕"""
        try:
            # 正規化路徑，確保在Windows上正確處理
            video_path = os.path.normpath(video_path)
            subtitle_path = os.path.normpath(subtitle_path)
            output_path = os.path.normpath(output_path)

            # 在Windows上，FFmpeg需要使用正斜杠或雙反斜杠
            subtitle_path_for_ffmpeg = subtitle_path.replace('\\', '/')

            # 構建ffmpeg命令
            ffmpeg_cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vf', f"ass={subtitle_path_for_ffmpeg}",
                '-c:a', 'copy',
                '-y',  # 覆蓋輸出文件
                output_path
            ]

            # 如果配置了ffmpeg路徑，使用指定路徑
            if settings.ffmpeg_path:
                ffmpeg_cmd[0] = settings.ffmpeg_path

            # 執行ffmpeg命令
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                check=True
            )

            return output_path

        except subprocess.CalledProcessError as e:
            print(f"FFmpeg error: {e.stderr}")
            # 如果ffmpeg失敗，返回原視頻路徑
            return video_path
        except Exception as e:
            print(f"Error embedding subtitles: {e}")
            return video_path

    def _add_intro_outro(self, main_clip: VideoFileClip, config: Dict[str, Any]) -> VideoFileClip:
        """添加片頭片尾"""
        try:
            clips = []

            # 添加片頭
            if config.get('intro_path') and os.path.exists(config['intro_path']):
                intro_clip = VideoFileClip(config['intro_path'])
                clips.append(intro_clip)

            # 添加主視頻
            clips.append(main_clip)

            # 添加片尾
            if config.get('outro_path') and os.path.exists(config['outro_path']):
                outro_clip = VideoFileClip(config['outro_path'])
                clips.append(outro_clip)

            # 如果只有主視頻，直接返回
            if len(clips) == 1:
                return main_clip

            # 連接所有視頻片段
            final_clip = concatenate_videoclips(clips, method="compose")

            return final_clip

        except Exception as e:
            print(f"Error adding intro/outro: {e}")
            return main_clip

    def trim_video(self, video_path: str, start_time: float, end_time: float) -> str:
        """裁剪視頻"""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            # 加載視頻
            video = VideoFileClip(video_path)

            # 裁剪視頻
            trimmed_video = video.subclip(start_time, end_time)

            # 生成輸出路徑
            output_dir = os.path.join(settings.project_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, f"trimmed_{start_time}_{end_time}.mp4")

            # 輸出視頻
            trimmed_video.write_videofile(output_path)

            # 清理資源
            video.close()
            trimmed_video.close()

            return output_path

        except Exception as e:
            print(f"Error trimming video: {e}")
            raise

    def resize_video(self, video_path: str, width: int, height: int) -> str:
        """調整視頻尺寸"""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            # 加載視頻
            video = VideoFileClip(video_path)

            # 調整尺寸 - 使用改進的resize方法
            try:
                resized_video = video.resize((width, height))
                print(f"Successfully resized video to {width}x{height}")
            except Exception as resize_error:
                print(f"Standard resize failed: {resize_error}")
                # 使用備用的FFmpeg方法
                resized_video = self._ffmpeg_resize(video, width, height)

            # 生成輸出路徑
            output_dir = os.path.join(settings.project_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, f"resized_{width}x{height}.mp4")

            # 輸出視頻
            resized_video.write_videofile(output_path)

            # 清理資源
            video.close()
            resized_video.close()

            return output_path

        except Exception as e:
            print(f"Error resizing video: {e}")
            raise

    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """獲取視頻信息"""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            video = VideoFileClip(video_path)

            info = {
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None,
                "file_size": os.path.getsize(video_path)
            }

            # 清理資源
            video.close()

            return info

        except Exception as e:
            print(f"Error getting video info: {e}")
            return {}

    def create_preview(self, video_path: str, timestamp: float = None) -> str:
        """創建視頻預覽圖"""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            video = VideoFileClip(video_path)

            # 如果沒有指定時間戳，使用視頻中點
            if timestamp is None:
                timestamp = video.duration / 2

            # 確保時間戳在有效範圍內
            timestamp = max(0, min(timestamp, video.duration))

            # 獲取幀
            frame = video.get_frame(timestamp)

            # 生成輸出路徑
            output_dir = os.path.join(settings.project_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, f"preview_{timestamp}.jpg")

            # 保存圖片
            from PIL import Image
            img = Image.fromarray(frame)
            img.save(output_path, "JPEG", quality=85)

            # 清理資源
            video.close()

            return output_path

        except Exception as e:
            print(f"Error creating preview: {e}")
            raise