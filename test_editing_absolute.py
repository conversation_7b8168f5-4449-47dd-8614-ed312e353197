#!/usr/bin/env python3
"""Test editing API with absolute path"""

import requests
import os

def test_editing_absolute():
    """Test editing API with absolute path"""
    print("🔍 Testing editing API with absolute path...")
    
    # Create a dummy video file for testing with absolute path
    test_video_path = os.path.abspath("storage/test_video.mp4")
    os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
    
    # Create a dummy file
    with open(test_video_path, "w") as f:
        f.write("dummy video content")
    
    print(f"📋 Created test file: {test_video_path}")
    print(f"📋 File exists: {os.path.exists(test_video_path)}")
    
    # Use a known project ID
    project_id = "b9da6931-653e-4a2c-a575-a26dadeb6c08"
    
    # Test editing endpoint with absolute file path
    editing_data = {
        'project_id': project_id,
        'main_video_path': test_video_path,
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print(f"📋 Testing with project ID: {project_id}")
    print(f"📋 Editing data: {editing_data}")
    
    try:
        response = requests.post('http://localhost:8000/api/editing/start', json=editing_data)
        print(f"Editing API status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Editing started successfully!")
            print(f"📋 Job ID: {result.get('job_id')}")
        elif response.status_code == 500:
            print("❌ 500 Internal Server Error detected!")
        else:
            print(f"📋 Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")
    
    # Clean up
    if os.path.exists(test_video_path):
        os.remove(test_video_path)
        print(f"🧹 Cleaned up test file: {test_video_path}")

if __name__ == "__main__":
    test_editing_absolute()
