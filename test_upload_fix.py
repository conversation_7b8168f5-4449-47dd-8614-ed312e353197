#!/usr/bin/env python3
"""Test upload functionality"""

import requests
import os
import tempfile

def create_test_file():
    """Create a small test video file"""
    # Create a temporary file with video extension
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as f:
        # Write some dummy content
        f.write(b'fake video content for testing')
        return f.name

def test_upload():
    """Test file upload functionality"""
    print("🔍 Testing file upload...")
    
    # Create test file
    test_file_path = create_test_file()
    
    try:
        # Prepare upload data
        files = {
            'files': ('test_video.mp4', open(test_file_path, 'rb'), 'video/mp4')
        }
        data = {
            'project_name': 'Test Upload Project',
            'project_description': 'This is a test upload project'
        }
        
        # Upload file
        response = requests.post('http://localhost:3000/api/uploads/', files=files, data=data)
        
        print(f"Upload status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Upload successful!")
            print(f"📋 Project ID: {result.get('project_id')}")
            print(f"📋 Files uploaded: {len(result.get('files', []))}")
            
            # Test if project can be retrieved
            project_id = result.get('project_id')
            if project_id:
                project_response = requests.get(f'http://localhost:3000/api/projects/{project_id}')
                print(f"Project retrieval status: {project_response.status_code}")
                
                if project_response.status_code == 200:
                    project = project_response.json()
                    print(f"✅ Project retrieved successfully: {project.get('name')}")
                else:
                    print(f"❌ Project retrieval failed: {project_response.text}")
            
            return result
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None
    finally:
        # Clean up test file
        if os.path.exists(test_file_path):
            os.unlink(test_file_path)
        files['files'][1].close()

def test_editing_api():
    """Test editing API endpoint"""
    print("\n🔍 Testing editing API...")
    
    try:
        # Test editing endpoint with dummy data
        editing_data = {
            'project_id': 'test-project-id',
            'main_video_path': 'test/path/video.mp4',
            'subtitle_effect': 'none',
            'background_volume': 0.3,
            'audio_volume': 1.0
        }
        
        response = requests.post('http://localhost:3000/api/editing/start', json=editing_data)
        print(f"Editing API status: {response.status_code}")
        
        if response.status_code == 404:
            print("⚠️ Editing API returns 404 - this is expected for non-existent project")
        elif response.status_code == 422:
            print("⚠️ Editing API returns 422 - validation error, but endpoint exists")
        else:
            print(f"📋 Editing API response: {response.text}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")

if __name__ == "__main__":
    print("🚀 Testing upload functionality...")
    
    # Test upload
    upload_result = test_upload()
    
    # Test editing API
    test_editing_api()
    
    print("\n✅ Upload testing complete!")
    if upload_result:
        print(f"📋 You can check the uploaded project at: http://localhost:3000/projects/{upload_result.get('project_id')}")
