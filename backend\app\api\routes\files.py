from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import FileResponse, StreamingResponse
import os
import mimetypes
from app.core.config import settings
from app.models.project import remove_project_file, get_project_files
from app.core.database import get_db

router = APIRouter()

@router.delete("/{file_id}")
async def delete_file(file_id: int):
    """刪除文件"""
    try:
        # 首先獲取文件信息
        db = get_db()
        query = "SELECT * FROM project_files WHERE id = ?"
        files = db.execute_query(query, (file_id,))
        
        if not files:
            raise HTTPException(status_code=404, detail="File not found")
        
        file_info = files[0]
        file_path = file_info['file_path']
        
        # 刪除數據庫記錄
        print(f"Attempting to delete file record with ID: {file_id}")
        success = remove_project_file(file_id)
        print(f"Delete result: {success}")
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete file record")
        
        # 刪除物理文件
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError as e:
                # 即使物理文件刪除失敗，數據庫記錄已經刪除，記錄警告但不拋出錯誤
                print(f"Warning: Failed to delete physical file {file_path}: {e}")
        
        return {
            "message": "File deleted successfully",
            "file_id": file_id
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")

@router.get("/{file_id}/preview")
async def preview_file(file_id: int):
    """預覽文件"""
    try:
        # 獲取文件信息
        db = get_db()
        query = "SELECT * FROM project_files WHERE id = ?"
        files = db.execute_query(query, (file_id,))
        
        if not files:
            raise HTTPException(status_code=404, detail="File not found")
        
        file_info = files[0]
        file_path = file_info['file_path']
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Physical file not found")
        
        # 獲取文件的 MIME 類型
        content_type = file_info.get('content_type')
        if not content_type:
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                content_type = 'application/octet-stream'
        
        return FileResponse(
            path=file_path,
            media_type=content_type,
            filename=file_info['filename']
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to preview file: {str(e)}")

@router.get("/{file_id}/download")
async def download_file(file_id: int):
    """下載文件"""
    try:
        # 獲取文件信息
        db = get_db()
        query = "SELECT * FROM project_files WHERE id = ?"
        files = db.execute_query(query, (file_id,))
        
        if not files:
            raise HTTPException(status_code=404, detail="File not found")
        
        file_info = files[0]
        file_path = file_info['file_path']
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Physical file not found")
        
        # 獲取文件的 MIME 類型
        content_type = file_info.get('content_type')
        if not content_type:
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                content_type = 'application/octet-stream'
        
        return FileResponse(
            path=file_path,
            media_type=content_type,
            filename=file_info['filename'],
            headers={"Content-Disposition": f"attachment; filename={file_info['filename']}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download file: {str(e)}")
