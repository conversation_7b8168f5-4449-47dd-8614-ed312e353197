#!/usr/bin/env python3
"""Debug API issues"""

import requests
import json

def test_direct_api():
    """Test direct API call"""
    print("🔍 Testing direct API call to backend...")
    try:
        response = requests.get("http://localhost:8000/api/projects/")
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text[:500]}...")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Direct API call successful, got {len(data)} projects")
            return data
        else:
            print(f"❌ Direct API call failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Direct API call error: {e}")
        return None

def test_proxy_api():
    """Test API call through frontend proxy"""
    print("\n🔍 Testing API call through frontend proxy...")
    try:
        response = requests.get("http://localhost:3000/api/projects/")
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text[:500]}...")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Proxy API call successful, got {len(data)} projects")
            return data
        else:
            print(f"❌ Proxy API call failed: {response.status_code}")
            print(f"Error content: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Proxy API call error: {e}")
        return None

def test_health_check():
    """Test health check"""
    print("\n🔍 Testing health check...")
    try:
        # Direct
        response = requests.get("http://localhost:8000/health")
        print(f"Direct health check: {response.status_code} - {response.json()}")
        
        # Through proxy
        response = requests.get("http://localhost:3000/api/../health")
        print(f"Proxy health check: {response.status_code} - {response.json()}")
        
    except Exception as e:
        print(f"❌ Health check error: {e}")

if __name__ == "__main__":
    print("🚀 Starting API debugging...")
    
    test_health_check()
    direct_data = test_direct_api()
    proxy_data = test_proxy_api()
    
    print("\n📋 Summary:")
    print(f"- Direct API: {'✅ Working' if direct_data else '❌ Failed'}")
    print(f"- Proxy API: {'✅ Working' if proxy_data else '❌ Failed'}")
