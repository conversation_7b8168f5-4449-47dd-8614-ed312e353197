@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 前端服務

echo ========================================
echo 🎨 自動視頻剪輯平台 - 前端服務
echo ========================================
echo.

:: 檢查前端目錄
if not exist "frontend" (
    echo ❌ 前端目錄不存在
    pause
    exit /b 1
)

:: 檢查 node_modules
if not exist "frontend\node_modules" (
    echo ❌ 前端依賴未安裝，請先運行 setup_windows.bat
    pause
    exit /b 1
)

echo 🚀 啟動前端服務...
echo.
echo 📋 服務信息：
echo - 前端地址: http://localhost:3000
echo - 開發模式: 支持熱重載
echo.
echo 💡 按 Ctrl+C 停止服務
echo.

cd frontend
npm run dev
