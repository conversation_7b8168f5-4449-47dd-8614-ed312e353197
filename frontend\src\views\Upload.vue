<template>
  <div class="upload-page">
    <div class="page-header">
      <h1>文件上傳</h1>
      <p>支持視頻、音頻、字幕文件上傳，自動創建項目並開始處理</p>
    </div>

    <!-- 上傳區域 -->
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <span>選擇文件</span>
          <el-button v-if="uploadedFiles.length > 0" @click="clearFiles" type="danger" plain>
            清空文件
          </el-button>
        </div>
      </template>

      <el-upload
        ref="uploadRef"
        class="upload-dragger"
        drag
        :action="uploadAction"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        :accept="acceptedTypes"
        :show-file-list="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖到此處，或<em>點擊上傳</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 mp4, avi, mov, mkv, mp3, wav, aac, srt, ass, vtt 格式文件
          </div>
        </template>
      </el-upload>

      <!-- 文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="file-list">
        <h3>已選擇的文件</h3>
        <el-table :data="uploadedFiles" style="width: 100%">
          <el-table-column prop="name" label="文件名" min-width="200" />
          <el-table-column prop="size" label="大小" width="120">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="類型" width="100">
            <template #default="{ row }">
              <el-tag :type="getFileTypeColor(row.type)">
                {{ getFileTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row, $index }">
              <el-button size="small" type="danger" @click="removeFile($index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 項目配置 -->
    <el-card v-if="uploadedFiles.length > 0" class="config-card">
      <template #header>
        <span>項目配置</span>
      </template>

      <el-form
        ref="configForm"
        :model="projectConfig"
        :rules="configRules"
        label-width="120px"
      >
        <el-form-item label="項目名稱" prop="name">
          <el-input v-model="projectConfig.name" placeholder="請輸入項目名稱" />
        </el-form-item>
        <el-form-item label="項目描述">
          <el-input
            v-model="projectConfig.description"
            type="textarea"
            rows="3"
            placeholder="請輸入項目描述（可選）"
          />
        </el-form-item>
        <el-form-item label="自動開始編輯">
          <el-switch v-model="projectConfig.autoStart" />
          <span class="form-tip">上傳完成後自動開始視頻編輯</span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 編輯配置 -->
    <el-card v-if="projectConfig.autoStart && hasVideoFile" class="editing-config-card">
      <template #header>
        <span>編輯配置</span>
      </template>

      <el-form :model="editingConfig" label-width="120px">
        <el-form-item label="字幕效果">
          <el-select v-model="editingConfig.subtitle_effect" placeholder="選擇字幕效果">
            <el-option label="無效果" value="none" />
            <el-option label="卡拉OK" value="karaoke" />
            <el-option label="打字機" value="typewriter" />
            <el-option label="發光" value="glow" />
            <el-option label="陰影" value="shadow" />
          </el-select>
        </el-form-item>
        <el-form-item label="背景音樂音量">
          <el-slider
            v-model="editingConfig.background_volume"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
          />
        </el-form-item>
        <el-form-item label="音頻音量">
          <el-slider
            v-model="editingConfig.audio_volume"
            :min="0"
            :max="2"
            :step="0.1"
            show-input
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 上傳按鈕 -->
    <div v-if="uploadedFiles.length > 0" class="upload-actions">
      <el-button size="large" @click="clearFiles">取消</el-button>
      <el-button
        type="primary"
        size="large"
        @click="startUpload"
        :loading="uploading"
        :disabled="uploadedFiles.length === 0"
      >
        {{ uploading ? '上傳中...' : '開始上傳' }}
      </el-button>
    </div>

    <!-- 上傳進度 -->
    <el-card v-if="uploading" class="progress-card">
      <template #header>
        <span>上傳進度</span>
      </template>
      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="20"
      />
      <p class="progress-text">{{ progressText }}</p>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { uploadsAPI, editingAPI } from '@/services/api'
import { useProjectsStore } from '@/stores/projects'
import { ElMessage } from 'element-plus'
import { UploadFilled, Delete } from '@element-plus/icons-vue'

export default {
  name: 'Upload',
  components: {
    UploadFilled,
    Delete
  },
  setup() {
    const router = useRouter()
    const projectsStore = useProjectsStore()
    const uploadRef = ref(null)
    const configForm = ref(null)

    // 狀態
    const uploadedFiles = ref([])
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const uploadStatus = ref('')
    const progressText = ref('')

    // 配置
    const projectConfig = reactive({
      name: '',
      description: '',
      autoStart: false
    })

    const editingConfig = reactive({
      subtitle_effect: 'none',
      background_volume: 0.3,
      audio_volume: 1.0
    })

    // 表單驗證
    const configRules = {
      name: [
        { required: true, message: '請輸入項目名稱', trigger: 'blur' },
        { min: 2, max: 50, message: '長度在 2 到 50 個字符', trigger: 'blur' }
      ]
    }

    // 計算屬性
    const uploadAction = computed(() => '/api/uploads/')
    const acceptedTypes = computed(() => '.mp4,.avi,.mov,.mkv,.mp3,.wav,.aac,.srt,.ass,.vtt')
    
    const hasVideoFile = computed(() => {
      return uploadedFiles.value.some(file => file.type === 'video')
    })

    // 方法
    const handleFileChange = (file, fileList) => {
      const fileType = getFileType(file.name)
      const fileInfo = {
        name: file.name,
        size: file.size,
        type: fileType,
        raw: file.raw
      }
      
      uploadedFiles.value.push(fileInfo)
      
      // 自動生成項目名稱
      if (!projectConfig.name && fileType === 'video') {
        projectConfig.name = file.name.replace(/\.[^/.]+$/, '')
      }
    }

    const handleFileRemove = (file, fileList) => {
      // 這個方法在我們的實現中不會被調用，因為我們設置了 show-file-list="false"
    }

    const removeFile = (index) => {
      uploadedFiles.value.splice(index, 1)
      
      // 如果刪除的是視頻文件，重新生成項目名稱
      if (uploadedFiles.value.length > 0) {
        const videoFile = uploadedFiles.value.find(f => f.type === 'video')
        if (videoFile && !projectConfig.name) {
          projectConfig.name = videoFile.name.replace(/\.[^/.]+$/, '')
        }
      }
    }

    const clearFiles = () => {
      uploadedFiles.value = []
      projectConfig.name = ''
      projectConfig.description = ''
      projectConfig.autoStart = false
    }

    const beforeUpload = (file) => {
      const fileType = getFileType(file.name)
      if (!fileType) {
        ElMessage.error('不支持的文件格式')
        return false
      }
      
      const maxSize = 500 * 1024 * 1024 // 500MB
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超過 500MB')
        return false
      }
      
      return true
    }

    const startUpload = async () => {
      if (!configForm.value) return

      await configForm.value.validate(async (valid) => {
        if (valid) {
          uploading.value = true
          uploadProgress.value = 0
          uploadStatus.value = ''
          progressText.value = '準備上傳...'

          try {
            // 創建 FormData
            const formData = new FormData()
            formData.append('project_name', projectConfig.name)
            if (projectConfig.description) {
              formData.append('project_description', projectConfig.description)
            }

            uploadedFiles.value.forEach(file => {
              formData.append('files', file.raw)
            })

            // 上傳文件
            const response = await uploadsAPI.uploadFiles(formData, (progressEvent) => {
              uploadProgress.value = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              progressText.value = `上傳中... ${uploadProgress.value}%`
            })

            uploadStatus.value = 'success'
            progressText.value = '上傳完成！'
            ElMessage.success('文件上傳成功')

            // 如果設置了自動開始編輯
            if (projectConfig.autoStart && hasVideoFile.value) {
              await startEditing(response.project_id)
            }

            // 跳轉到項目詳情頁
            setTimeout(() => {
              router.push(`/projects/${response.project_id}`)
            }, 1000)

          } catch (error) {
            uploadStatus.value = 'exception'
            progressText.value = '上傳失敗'
            ElMessage.error('文件上傳失敗')
          } finally {
            uploading.value = false
          }
        }
      })
    }

    const startEditing = async (projectId) => {
      try {
        const videoFile = uploadedFiles.value.find(f => f.type === 'video')
        const subtitleFile = uploadedFiles.value.find(f => f.type === 'subtitle')
        const audioFile = uploadedFiles.value.find(f => f.type === 'audio')

        const editingData = {
          project_id: projectId,
          main_video_path: `storage/uploads/${projectId}/${videoFile.name}`,
          ...editingConfig
        }

        if (subtitleFile) {
          editingData.subtitle_path = `storage/uploads/${projectId}/${subtitleFile.name}`
        }

        if (audioFile) {
          editingData.background_music_path = `storage/uploads/${projectId}/${audioFile.name}`
        }

        await editingAPI.startEditing(editingData)
        ElMessage.success('編輯任務已開始')
      } catch (error) {
        ElMessage.error('啟動編輯任務失敗')
      }
    }

    const getFileType = (filename) => {
      const ext = filename.toLowerCase().split('.').pop()
      const videoExts = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv']
      const audioExts = ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a']
      const subtitleExts = ['srt', 'ass', 'ssa', 'vtt']

      if (videoExts.includes(ext)) return 'video'
      if (audioExts.includes(ext)) return 'audio'
      if (subtitleExts.includes(ext)) return 'subtitle'
      return null
    }

    const getFileTypeText = (type) => {
      const typeMap = {
        'video': '視頻',
        'audio': '音頻',
        'subtitle': '字幕'
      }
      return typeMap[type] || type
    }

    const getFileTypeColor = (type) => {
      const colorMap = {
        'video': 'primary',
        'audio': 'success',
        'subtitle': 'warning'
      }
      return colorMap[type] || 'info'
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    return {
      uploadRef,
      configForm,
      uploadedFiles,
      uploading,
      uploadProgress,
      uploadStatus,
      progressText,
      projectConfig,
      editingConfig,
      configRules,
      uploadAction,
      acceptedTypes,
      hasVideoFile,
      handleFileChange,
      handleFileRemove,
      removeFile,
      clearFiles,
      beforeUpload,
      startUpload,
      getFileTypeText,
      getFileTypeColor,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.upload-page {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

.upload-card,
.config-card,
.editing-config-card,
.progress-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-dragger {
  width: 100%;
}

.file-list {
  margin-top: 20px;
}

.file-list h3 {
  margin-bottom: 15px;
  color: #303133;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.upload-actions {
  text-align: center;
  margin: 30px 0;
}

.upload-actions .el-button {
  margin: 0 10px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
}
</style>
