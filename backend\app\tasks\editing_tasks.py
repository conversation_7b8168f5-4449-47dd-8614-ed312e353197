from huey import SqliteHuey
from app.services.video_editor import VideoEditor
from app.models.job import update_job_status, get_job

# 使用SQLite作為Huey的存儲
huey = SqliteHuey('video_editing_tasks', filename='storage/huey.db')

@huey.task()
def process_video_task(job_id, main_video_path, config):
    """處理視頻編輯的後台任務"""
    try:
        # 更新任務狀態為進行中
        update_job_status(job_id, 'processing', 0)
        
        # 創建編輯器實例
        editor = VideoEditor(config['project_id'])
        
        # 處理視頻
        output_path = editor.process_video(main_video_path, config)
        
        # 更新任務狀態為完成
        update_job_status(job_id, 'completed', 100, output_path=output_path)
        
        return output_path
    except Exception as e:
        # 更新任務狀態為失敗
        update_job_status(job_id, 'failed', 0, error=str(e))
        raise