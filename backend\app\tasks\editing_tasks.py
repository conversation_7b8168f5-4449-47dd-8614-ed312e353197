from huey import SqliteHuey
from app.services.video_editor import VideoEditor
from app.models.job import update_job_status, get_job, update_job_progress
from app.core.config import settings
import os

# 使用SQLite作為Huey的存儲
huey = SqliteHuey('video_editing_tasks', filename=settings.huey_db_path)

@huey.task()
def process_video_task(job_id: str, middle_video_paths: list, config: dict):
    """處理視頻編輯的後台任務"""
    try:
        # 更新任務狀態為進行中
        update_job_status(job_id, 'processing', 0)

        # 創建進度回調函數
        def progress_callback(progress: int):
            update_job_progress(job_id, progress)

        # 創建編輯器實例
        editor = VideoEditor(config['project_id'], progress_callback=progress_callback)

        # 處理視頻
        output_path = editor.process_video(middle_video_paths, config)

        # 驗證輸出文件是否存在
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Output file not created: {output_path}")

        # 更新任務狀態為完成
        update_job_status(job_id, 'completed', 100, output_path=output_path)

        return output_path

    except Exception as e:
        error_message = f"Video processing failed: {str(e)}"
        print(error_message)

        # 更新任務狀態為失敗
        update_job_status(job_id, 'failed', 0, error=error_message)
        raise

@huey.task()
def trim_video_task(job_id: str, video_path: str, start_time: float, end_time: float, project_id: str):
    """裁剪視頻的後台任務"""
    try:
        update_job_status(job_id, 'processing', 0)

        editor = VideoEditor(project_id)
        output_path = editor.trim_video(video_path, start_time, end_time)

        update_job_status(job_id, 'completed', 100, output_path=output_path)
        return output_path

    except Exception as e:
        error_message = f"Video trimming failed: {str(e)}"
        update_job_status(job_id, 'failed', 0, error=error_message)
        raise

@huey.task()
def resize_video_task(job_id: str, video_path: str, width: int, height: int, project_id: str):
    """調整視頻尺寸的後台任務"""
    try:
        update_job_status(job_id, 'processing', 0)

        editor = VideoEditor(project_id)
        output_path = editor.resize_video(video_path, width, height)

        update_job_status(job_id, 'completed', 100, output_path=output_path)
        return output_path

    except Exception as e:
        error_message = f"Video resizing failed: {str(e)}"
        update_job_status(job_id, 'failed', 0, error=error_message)
        raise

@huey.task()
def create_preview_task(job_id: str, video_path: str, timestamp: float, project_id: str):
    """創建視頻預覽的後台任務"""
    try:
        update_job_status(job_id, 'processing', 0)

        editor = VideoEditor(project_id)
        output_path = editor.create_preview(video_path, timestamp)

        update_job_status(job_id, 'completed', 100, output_path=output_path)
        return output_path

    except Exception as e:
        error_message = f"Preview creation failed: {str(e)}"
        update_job_status(job_id, 'failed', 0, error=error_message)
        raise