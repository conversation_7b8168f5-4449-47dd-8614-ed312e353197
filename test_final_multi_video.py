#!/usr/bin/env python3
"""
最终测试多视频拼接功能
"""
import requests
import json
import time

def test_final_multi_video():
    """最终测试多视频拼接"""
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    base_url = "http://localhost:8000"
    
    print("🎬 最终测试多视频拼接功能")
    print("=" * 50)
    
    # 选择3个不同大小的视频进行测试
    selected_videos = ["1.MOV", "2.MOV", "3.MOV"]  # 较小的文件，处理更快
    
    config = {
        "project_id": project_id,
        "middle_video_filenames": selected_videos,
        "output_size": "1080x1920",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"📝 编辑配置:")
    print(f"   片中视频: {selected_videos}")
    print(f"   视频数量: {len(selected_videos)}")
    print(f"   输出尺寸: {config['output_size']}")
    
    # 启动编辑任务
    print(f"\n🚀 启动编辑任务...")
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    
    if response.status_code != 200:
        print(f"❌ 启动失败: {response.status_code}")
        print(f"错误: {response.text}")
        return
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ 任务已启动，ID: {job_id}")
    
    # 监控进度
    print(f"\n📊 监控进度...")
    start_time = time.time()
    
    for i in range(1, 31):  # 最多检查30次 (1分钟)
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            time.sleep(2)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        elapsed = time.time() - start_time
        
        print(f"   进度: {progress}% | 状态: {status} | 耗时: {elapsed:.1f}s")
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"✅ 编辑完成!")
            print(f"📁 输出文件: {output_path}")
            return True
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"❌ 编辑失败: {error}")
            return False
        
        if i < 30:
            time.sleep(2)
    
    print(f"⏰ 监控超时")
    return False

if __name__ == "__main__":
    success = test_final_multi_video()
    
    if success:
        print(f"\n🎉 多视频拼接测试成功!")
        print(f"💡 现在您可以在前端选择多个片中视频进行编辑了!")
    else:
        print(f"\n💥 多视频拼接测试失败")
