#!/usr/bin/env python3
"""
使用正確路徑測試剪輯功能
"""

import requests
import json
import os

def test_editing_with_absolute_path():
    """使用絕對路徑測試剪輯功能"""
    base_url = "http://localhost:8000"
    
    # 項目信息
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    video_filename = "0.MOV"
    
    # 構建絕對路徑
    current_dir = os.getcwd()
    video_path = os.path.join(current_dir, "backend", "storage", "uploads", project_id, video_filename)
    
    print("🎬 使用正確路徑測試剪輯功能")
    print("=" * 50)
    print(f"當前目錄: {current_dir}")
    print(f"視頻路徑: {video_path}")
    print(f"文件存在: {os.path.exists(video_path)}")
    
    if not os.path.exists(video_path):
        print("❌ 視頻文件不存在，無法進行測試")
        return False
    
    # 剪輯配置
    editing_config = {
        "project_id": project_id,
        "main_video_path": video_path,  # 使用絕對路徑
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "output_size": "original"
    }
    
    print(f"\n📋 剪輯配置:")
    print(f"   項目ID: {project_id}")
    print(f"   主視頻路徑: {video_path}")
    print(f"   字幕效果: {editing_config['subtitle_effect']}")
    
    try:
        # 發送剪輯請求
        print(f"\n🚀 發送剪輯請求...")
        response = requests.post(
            f"{base_url}/api/editing/start",
            json=editing_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            print(f"✅ 剪輯任務已創建!")
            print(f"   任務ID: {job_id}")
            print(f"   狀態: {result.get('status', 'unknown')}")
            
            print(f"\n💡 監控任務進度:")
            print(f"   API: GET {base_url}/api/progress/{job_id}")
            print(f"   前端: http://localhost:3001/tasks")
            
            return True
        else:
            print(f"❌ 創建剪輯任務失敗: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"錯誤詳情: {error_detail}")
            except:
                print(f"錯誤內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試剪輯功能錯誤: {e}")
        return False

if __name__ == "__main__":
    test_editing_with_absolute_path()
