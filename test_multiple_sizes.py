#!/usr/bin/env python3
"""
测试多种视频尺寸调整
"""
import requests
import json
import time
import os
from moviepy.editor import VideoFile<PERSON>lip

def test_size(target_size, description):
    """测试特定尺寸"""
    base_url = "http://localhost:8000"
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    print(f"\n🧪 测试 {description} ({target_size})")
    print("-" * 40)
    
    config = {
        "project_id": project_id,
        "middle_video_filenames": ["1.MOV"],  # 使用较小的视频
        "output_size": target_size,
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    # 启动编辑任务
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    if response.status_code != 200:
        print(f"❌ 启动失败: {response.status_code}")
        return False
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"📝 任务ID: {job_id}")
    
    # 监控进度
    start_time = time.time()
    for i in range(1, 21):  # 最多检查20次
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            time.sleep(2)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        elapsed = time.time() - start_time
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"✅ 完成 (耗时: {elapsed:.1f}s)")
            
            # 检查输出尺寸
            full_output_path = f"backend/{output_path}"
            if os.path.exists(full_output_path):
                try:
                    clip = VideoFileClip(full_output_path)
                    actual_size = clip.size
                    clip.close()
                    
                    # 解析期望尺寸
                    if 'x' in target_size:
                        expected_w, expected_h = map(int, target_size.split('x'))
                        expected_size = (expected_w, expected_h)
                    else:
                        expected_size = "unknown"
                    
                    print(f"📊 期望: {expected_size}")
                    print(f"📊 实际: {actual_size}")

                    # 转换为相同类型进行比较
                    if expected_size != "unknown":
                        actual_tuple = tuple(actual_size)
                        expected_tuple = tuple(expected_size)

                        if actual_tuple == expected_tuple:
                            print(f"✅ 尺寸正确!")
                            return True
                        else:
                            print(f"❌ 尺寸不匹配")
                            print(f"   实际元组: {actual_tuple}")
                            print(f"   期望元组: {expected_tuple}")
                            return False
                    else:
                        print(f"❌ 无法验证尺寸")
                        return False
                        
                except Exception as e:
                    print(f"❌ 检查视频出错: {e}")
                    return False
            else:
                print(f"❌ 输出文件不存在")
                return False
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"❌ 失败: {error}")
            return False
        
        if i < 20:
            time.sleep(2)
    
    print(f"⏰ 超时")
    return False

def main():
    """测试多种尺寸"""
    print("🔧 测试多种视频尺寸调整")
    print("=" * 50)
    
    test_cases = [
        ("720x1280", "竖屏 720p"),
        ("1080x1920", "竖屏 1080p"),
        ("1920x1080", "横屏 1080p"),
        ("1280x720", "横屏 720p")
    ]
    
    results = []
    
    for target_size, description in test_cases:
        success = test_size(target_size, description)
        results.append((target_size, description, success))
        
        # 等待一下再进行下一个测试
        time.sleep(3)
    
    # 总结结果
    print(f"\n📊 测试结果总结")
    print("=" * 50)
    
    success_count = 0
    for target_size, description, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{description:12s} ({target_size:9s}): {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 成功")
    
    if success_count == len(results):
        print("🎉 所有尺寸调整都正常工作!")
        return True
    else:
        print("💥 部分尺寸调整存在问题")
        return False

if __name__ == "__main__":
    main()
