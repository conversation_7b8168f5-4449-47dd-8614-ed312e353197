#!/usr/bin/env python3
"""Test frontend fix"""

import requests
import json
import time

def test_frontend_api():
    """Test frontend API calls"""
    print("🔍 Testing frontend API calls...")
    
    # Test projects list through proxy
    try:
        response = requests.get("http://localhost:3000/api/projects/")
        print(f"✅ Projects list: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 Got {len(data)} projects")
            for project in data[:2]:  # Show first 2 projects
                print(f"  - {project.get('name', 'Unknown')} ({project.get('id', 'No ID')})")
        
    except Exception as e:
        print(f"❌ Projects list error: {e}")
    
    # Test health check
    try:
        response = requests.get("http://localhost:3000/api/../health")
        print(f"✅ Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"📋 Health: {response.json()}")
    except Exception as e:
        print(f"❌ Health check error: {e}")

def test_frontend_page():
    """Test frontend page loading"""
    print("\n🔍 Testing frontend page loading...")
    
    try:
        response = requests.get("http://localhost:3000")
        print(f"✅ Frontend page: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if "Auto Video Editor" in content:
                print("✅ Frontend content looks correct")
            else:
                print("⚠️ Frontend content might be incomplete")
        
    except Exception as e:
        print(f"❌ Frontend page error: {e}")

if __name__ == "__main__":
    print("🚀 Testing frontend fix...")
    test_frontend_api()
    test_frontend_page()
    print("\n✅ Frontend testing complete!")
    print("\n📋 Next steps:")
    print("1. Open http://localhost:3000 in browser")
    print("2. Click on '項目管理' in the navigation")
    print("3. Verify that projects load without 500 error")
