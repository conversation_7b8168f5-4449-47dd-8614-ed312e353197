#!/usr/bin/env python3
"""
完整測試視頻編輯功能
測試片頭、片中（多片）、片尾、字幕特效、音頻處理、背景音樂
"""
import requests
import json
import time
import os

def test_complete_editing_features():
    """完整測試所有編輯功能"""
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    base_url = "http://localhost:8000"
    
    print("🎬 完整測試視頻編輯功能")
    print("=" * 60)
    
    # 檢查項目文件
    print("📁 檢查項目文件...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/files")
    if response.status_code != 200:
        print(f"❌ 無法獲取項目文件: {response.status_code}")
        return False
    
    files = response.json()
    video_files = [f for f in files if f['filename'].lower().endswith(('.mp4', '.mov', '.avi'))]
    audio_files = [f for f in files if f['filename'].lower().endswith(('.mp3', '.wav', '.aac'))]
    subtitle_files = [f for f in files if f['filename'].lower().endswith(('.srt', '.ass', '.vtt'))]
    
    print(f"   📹 視頻文件: {len(video_files)} 個")
    for f in video_files[:5]:  # 只顯示前5個
        print(f"      - {f['filename']}")
    
    print(f"   🎵 音頻文件: {len(audio_files)} 個")
    for f in audio_files[:3]:  # 只顯示前3個
        print(f"      - {f['filename']}")
    
    print(f"   📝 字幕文件: {len(subtitle_files)} 個")
    for f in subtitle_files[:3]:  # 只顯示前3個
        print(f"      - {f['filename']}")
    
    if len(video_files) < 3:
        print("❌ 需要至少3個視頻文件來測試片頭、片中、片尾功能")
        return False
    
    # 構建完整的編輯配置
    config = {
        "project_id": project_id,
        
        # 片頭、片中、片尾配置
        "intro_filename": video_files[0]['filename'],  # 第一個視頻作為片頭
        "middle_video_filenames": [video_files[1]['filename'], video_files[2]['filename']],  # 中間兩個視頻作為片中
        "outro_filename": video_files[3]['filename'] if len(video_files) > 3 else video_files[0]['filename'],  # 片尾
        
        # 視頻設置
        "output_size": "1080x1920",
        
        # 音頻設置
        "audio_volume": 0.8,
        "background_volume": 0.3,
        "mute_original_audio": False,
        
        # 字幕設置
        "enable_subtitles": len(subtitle_files) > 0,
        "subtitle_effect": "karaoke" if len(subtitle_files) > 0 else "none",
        
        # 背景音樂
        "background_music_enabled": len(audio_files) > 0
    }
    
    # 添加字幕文件
    if len(subtitle_files) > 0:
        config["subtitle_filename"] = subtitle_files[0]['filename']
    
    # 添加背景音樂
    if len(audio_files) > 0:
        config["background_music_filename"] = audio_files[0]['filename']
    
    print(f"\n📝 完整編輯配置:")
    print(f"   🎬 片頭: {config.get('intro_filename', '無')}")
    print(f"   🎬 片中: {', '.join(config['middle_video_filenames'])}")
    print(f"   🎬 片尾: {config.get('outro_filename', '無')}")
    print(f"   📝 字幕: {config.get('subtitle_filename', '無')} (特效: {config.get('subtitle_effect', '無')})")
    print(f"   🎵 背景音樂: {config.get('background_music_filename', '無')}")
    print(f"   📊 輸出尺寸: {config['output_size']}")
    print(f"   🔊 音頻音量: {config['audio_volume']*100}%")
    print(f"   🎵 背景音量: {config['background_volume']*100}%")
    
    # 啟動編輯任務
    print(f"\n🚀 啟動完整編輯任務...")
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    
    if response.status_code != 200:
        print(f"❌ 啟動失敗: {response.status_code}")
        print(f"錯誤: {response.text}")
        return False
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ 任務已啟動，ID: {job_id}")
    
    # 監控進度
    print(f"\n📊 監控進度...")
    start_time = time.time()
    
    for i in range(1, 61):  # 最多檢查60次 (2分鐘)
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            time.sleep(2)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        elapsed = time.time() - start_time
        
        print(f"   進度: {progress}% | 狀態: {status} | 耗時: {elapsed:.1f}s")
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"✅ 編輯完成!")
            print(f"📁 輸出文件: {output_path}")
            return True
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"❌ 編輯失敗: {error}")
            return False
        
        if i < 60:
            time.sleep(2)
    
    print(f"⏰ 監控超時")
    return False

def verify_output_file():
    """驗證輸出文件"""
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    output_path = f"backend/storage/outputs/{project_id}/final_video.mp4"
    
    print(f"\n🔍 驗證輸出文件...")
    
    if not os.path.exists(output_path):
        print(f"❌ 輸出文件不存在: {output_path}")
        return False
    
    try:
        from moviepy.editor import VideoFileClip
        
        clip = VideoFileClip(output_path)
        duration = clip.duration
        size = clip.size
        fps = clip.fps
        has_audio = clip.audio is not None
        clip.close()
        
        file_size = os.path.getsize(output_path) / (1024 * 1024)
        
        print(f"📊 輸出文件分析:")
        print(f"   📁 文件大小: {file_size:.2f} MB")
        print(f"   📊 視頻尺寸: {size[0]}x{size[1]}")
        print(f"   ⏱️ 視頻時長: {duration:.2f}s")
        print(f"   🎞️ 幀率: {fps:.2f} fps")
        print(f"   🔊 音頻軌道: {'有' if has_audio else '無'}")
        
        # 分析結果
        success = True
        if size != (1080, 1920):
            print(f"   ⚠️ 尺寸不符合預期: {size[0]}x{size[1]} (期望: 1080x1920)")
            success = False
        
        if duration < 10:  # 片頭+片中+片尾應該超過10秒
            print(f"   ⚠️ 時長可能不正確: {duration:.2f}s (期望: >10s)")
            success = False
        
        if not has_audio:
            print(f"   ⚠️ 缺少音頻軌道")
            success = False
        
        if file_size < 1:
            print(f"   ⚠️ 文件大小異常: {file_size:.2f} MB")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 分析文件出錯: {e}")
        return False

if __name__ == "__main__":
    print("🎯 開始完整功能測試...")
    
    success = test_complete_editing_features()
    
    if success:
        print(f"\n🎉 編輯任務完成，開始驗證輸出...")
        verify_success = verify_output_file()
        
        if verify_success:
            print(f"\n✅ 完整功能測試成功!")
            print(f"🎬 片頭、片中（多片）、片尾功能正常")
            print(f"🎵 音頻和背景音樂處理正常")
            print(f"📝 字幕特效功能正常")
        else:
            print(f"\n⚠️ 編輯完成但輸出驗證失敗")
    else:
        print(f"\n💥 完整功能測試失敗")
