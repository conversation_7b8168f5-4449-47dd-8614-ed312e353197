@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 停止服務

echo ========================================
echo 🛑 自動視頻剪輯平台 - 停止服務
echo ========================================
echo.

echo 🔍 正在查找運行中的服務...

:: 停止 Node.js 進程（前端）
echo 🔧 停止前端服務...
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if not errorlevel 1 (
    taskkill /f /im node.exe >nul 2>&1
    if not errorlevel 1 (
        echo ✅ 前端服務已停止
    ) else (
        echo ⚠️  停止前端服務時出現問題
    )
) else (
    echo ℹ️  未找到運行中的前端服務
)

:: 停止 Python 進程（後端和任務隊列）
echo 🔧 停止後端服務和任務隊列...
tasklist /fi "imagename eq python.exe" 2>nul | find /i "python.exe" >nul
if not errorlevel 1 (
    :: 更精確地停止 uvicorn 和 huey 進程
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| find "python.exe"') do (
        set pid=%%i
        set pid=!pid:"=!
        wmic process where "ProcessId=!pid!" get CommandLine /format:csv 2>nul | find /i "uvicorn" >nul
        if not errorlevel 1 (
            taskkill /f /pid !pid! >nul 2>&1
            echo ✅ 後端服務已停止 (PID: !pid!)
        )
        wmic process where "ProcessId=!pid!" get CommandLine /format:csv 2>nul | find /i "huey" >nul
        if not errorlevel 1 (
            taskkill /f /pid !pid! >nul 2>&1
            echo ✅ 任務隊列已停止 (PID: !pid!)
        )
    )
) else (
    echo ℹ️  未找到運行中的 Python 服務
)

:: 檢查端口占用
echo.
echo 🔍 檢查端口占用情況...

:: 檢查 8000 端口（後端）
netstat -ano | find ":8000" >nul
if not errorlevel 1 (
    echo ⚠️  端口 8000 仍被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":8000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ 已強制釋放端口 8000
) else (
    echo ✅ 端口 8000 已釋放
)

:: 檢查 3000 端口（前端）
netstat -ano | find ":3000" >nul
if not errorlevel 1 (
    echo ⚠️  端口 3000 仍被占用
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":3000"') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ 已強制釋放端口 3000
) else (
    echo ✅ 端口 3000 已釋放
)

:: 關閉相關的命令行窗口
echo 🔧 關閉服務窗口...
tasklist /fi "windowtitle eq 後端服務*" >nul 2>&1
if not errorlevel 1 (
    taskkill /f /fi "windowtitle eq 後端服務*" >nul 2>&1
)

tasklist /fi "windowtitle eq 前端服務*" >nul 2>&1
if not errorlevel 1 (
    taskkill /f /fi "windowtitle eq 前端服務*" >nul 2>&1
)

tasklist /fi "windowtitle eq 任務隊列*" >nul 2>&1
if not errorlevel 1 (
    taskkill /f /fi "windowtitle eq 任務隊列*" >nul 2>&1
)

echo.
echo 🎉 所有服務已停止！
echo.
echo 📋 服務狀態：
echo - 前端服務: 已停止
echo - 後端服務: 已停止  
echo - 任務隊列: 已停止
echo.
echo 💡 如需重新啟動，請運行 start_windows.bat
echo.
pause
