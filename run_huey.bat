@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 任務隊列

echo ========================================
echo ⚙️  自動視頻剪輯平台 - 任務隊列
echo ========================================
echo.

:: 檢查虛擬環境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虛擬環境不存在，請先運行 setup_windows.bat
    pause
    exit /b 1
)

:: 激活虛擬環境
echo 🚀 激活虛擬環境...
call venv\Scripts\activate.bat

:: 檢查後端目錄
if not exist "backend" (
    echo ❌ 後端目錄不存在
    pause
    exit /b 1
)

echo 🚀 啟動任務隊列...
echo.
echo 📋 服務信息：
echo - 任務處理器: Huey
echo - 數據庫: SQLite
echo - 並發任務數: 2
echo.
echo 💡 按 Ctrl+C 停止服務
echo.

cd backend
python -m huey_consumer app.tasks.editing_tasks.huey
