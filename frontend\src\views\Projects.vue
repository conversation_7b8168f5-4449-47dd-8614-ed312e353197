<template>
  <div class="projects-page">
    <div class="page-header">
      <h1>項目管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新建項目
      </el-button>
    </div>

    <!-- 項目過濾器 -->
    <div class="filter-section">
      <el-card>
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="項目名稱">
            <el-input
              v-model="filterForm.name"
              placeholder="搜索項目名稱"
              clearable
              @input="debouncedSearch"
              @clear="loadProjects"
            />
          </el-form-item>
          <el-form-item label="狀態">
            <el-select
              v-model="filterForm.status"
              placeholder="選擇狀態"
              clearable
              @change="loadProjects"
            >
              <el-option label="進行中" value="active" />
              <el-option label="已完成" value="completed" />
              <el-option label="已歸檔" value="archived" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadProjects">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 項目列表 -->
    <div class="projects-list">
      <el-card v-loading="projectsStore.loading">
        <template #header>
          <div class="list-header">
            <span>項目列表</span>
            <div class="view-controls">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="grid">
                  <el-icon><Grid /></el-icon>
                </el-radio-button>
                <el-radio-button label="list">
                  <el-icon><List /></el-icon>
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 空狀態 -->
        <div v-if="projects.length === 0" class="empty-state">
          <el-empty description="暫無項目" />
        </div>

        <!-- 網格視圖 -->
        <el-row v-else-if="viewMode === 'grid'" :gutter="20">
          <el-col v-for="project in projects" :key="project.id" :span="8">
            <el-card class="project-card" @click="viewProject(project.id)">
              <div class="project-info">
                <h3>{{ project.name }}</h3>
                <p>{{ project.description || '無描述' }}</p>
                <div class="project-meta">
                  <span>{{ formatDate(project.created_at) }}</span>
                  <el-tag :type="getStatusType(project.status)" size="small">
                    {{ getStatusText(project.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="project-actions">
                <el-button-group>
                  <el-button size="small" @click.stop="editProject(project)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" @click.stop="confirmDelete(project)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-button-group>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 列表視圖 -->
        <el-table v-else :data="projects" style="width: 100%">
          <el-table-column prop="name" label="項目名稱" min-width="180">
            <template #default="{ row }">
              <el-link type="primary" @click="viewProject(row.id)">{{ row.name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="created_at" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click="viewProject(row.id)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button size="small" @click="editProject(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" type="danger" @click="confirmDelete(row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalProjects"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 創建項目對話框 -->
    <el-dialog
      v-model="createDialogVisible"
      :title="isEditing ? '編輯項目' : '創建新項目'"
      width="500px"
    >
      <el-form
        ref="projectForm"
        :model="projectFormData"
        :rules="projectRules"
        label-width="100px"
      >
        <el-form-item label="項目名稱" prop="name">
          <el-input v-model="projectFormData.name" placeholder="請輸入項目名稱" />
        </el-form-item>
        <el-form-item label="項目描述" prop="description">
          <el-input
            v-model="projectFormData.description"
            type="textarea"
            rows="3"
            placeholder="請輸入項目描述"
          />
        </el-form-item>
        <el-form-item label="項目狀態" prop="status">
          <el-select v-model="projectFormData.status" placeholder="請選擇狀態">
            <el-option label="進行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已歸檔" value="archived" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProject" :loading="submitting">
            {{ isEditing ? '保存' : '創建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 刪除確認對話框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="確認刪除"
      width="400px"
    >
      <p>確定要刪除項目 "{{ projectToDelete?.name }}" 嗎？此操作不可恢復。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteProject" :loading="submitting">
            確認刪除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { ElMessage } from 'element-plus'
import { Plus, Grid, List, Edit, Delete, View } from '@element-plus/icons-vue'

export default {
  name: 'Projects',
  components: {
    Plus,
    Grid,
    List,
    Edit,
    Delete,
    View
  },
  setup() {
    const router = useRouter()
    const projectsStore = useProjectsStore()
    const projectForm = ref(null)

    // 狀態
    const viewMode = ref('grid')
    const createDialogVisible = ref(false)
    const deleteDialogVisible = ref(false)
    const isEditing = ref(false)
    const submitting = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalProjects = ref(0)
    const projectToDelete = ref(null)

    // 表單數據
    const projectFormData = reactive({
      id: '',
      name: '',
      description: '',
      status: 'active'
    })

    // 過濾表單
    const filterForm = reactive({
      name: '',
      status: ''
    })

    // 表單驗證規則
    const projectRules = {
      name: [
        { required: true, message: '請輸入項目名稱', trigger: 'blur' },
        { min: 2, max: 50, message: '長度在 2 到 50 個字符', trigger: 'blur' }
      ]
    }

    // 計算屬性
    const projects = computed(() => projectsStore.projects)

    // 方法
    const loadProjects = async () => {
      try {
        const params = {
          page: currentPage.value,
          limit: pageSize.value,
          ...filterForm
        }
        const response = await projectsStore.fetchProjects(params)
        totalProjects.value = response.total || 0
      } catch (error) {
        ElMessage.error('加載項目失敗')
      }
    }

    const resetFilter = () => {
      filterForm.name = ''
      filterForm.status = ''
      loadProjects()
    }

    // 防抖搜尋功能
    let searchTimeout = null
    const debouncedSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        currentPage.value = 1 // 重置到第一頁
        loadProjects()
      }, 500) // 500ms 延遲
    }

    const handleSizeChange = (size) => {
      pageSize.value = size
      loadProjects()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadProjects()
    }

    const showCreateDialog = () => {
      isEditing.value = false
      projectFormData.id = ''
      projectFormData.name = ''
      projectFormData.description = ''
      projectFormData.status = 'active'
      createDialogVisible.value = true
    }

    const editProject = (project) => {
      isEditing.value = true
      projectFormData.id = project.id
      projectFormData.name = project.name
      projectFormData.description = project.description || ''
      projectFormData.status = project.status
      createDialogVisible.value = true
    }

    const submitProject = async () => {
      if (!projectForm.value) return

      await projectForm.value.validate(async (valid) => {
        if (valid) {
          submitting.value = true
          try {
            if (isEditing.value) {
              await projectsStore.updateProject(projectFormData.id, {
                name: projectFormData.name,
                description: projectFormData.description,
                status: projectFormData.status
              })
              ElMessage.success('項目更新成功')
            } else {
              await projectsStore.createProject({
                name: projectFormData.name,
                description: projectFormData.description,
                status: projectFormData.status
              })
              ElMessage.success('項目創建成功')
            }
            createDialogVisible.value = false
            loadProjects()
          } catch (error) {
            ElMessage.error(isEditing.value ? '更新項目失敗' : '創建項目失敗')
          } finally {
            submitting.value = false
          }
        }
      })
    }

    const confirmDelete = (project) => {
      projectToDelete.value = project
      deleteDialogVisible.value = true
    }

    const deleteProject = async () => {
      if (!projectToDelete.value) return

      submitting.value = true
      try {
        await projectsStore.deleteProject(projectToDelete.value.id)
        ElMessage.success('項目刪除成功')
        deleteDialogVisible.value = false
        loadProjects()
      } catch (error) {
        ElMessage.error('刪除項目失敗')
      } finally {
        submitting.value = false
      }
    }

    const viewProject = (id) => {
      router.push(`/projects/${id}`)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-TW')
    }

    const getStatusType = (status) => {
      const statusMap = {
        'active': 'success',
        'completed': 'info',
        'archived': 'warning'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '進行中',
        'completed': '已完成',
        'archived': '已歸檔'
      }
      return statusMap[status] || status
    }

    onMounted(() => {
      loadProjects()
    })

    return {
      projectsStore,
      projects,
      viewMode,
      createDialogVisible,
      deleteDialogVisible,
      isEditing,
      submitting,
      projectForm,
      projectFormData,
      projectRules,
      filterForm,
      currentPage,
      pageSize,
      totalProjects,
      projectToDelete,
      loadProjects,
      resetFilter,
      debouncedSearch,
      handleSizeChange,
      handleCurrentChange,
      showCreateDialog,
      editProject,
      submitProject,
      confirmDelete,
      deleteProject,
      viewProject,
      formatDate,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.projects-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.2s;
}

.project-card:hover {
  transform: translateY(-2px);
}

.project-info h3 {
  margin-bottom: 8px;
  color: #303133;
}

.project-info p {
  color: #606266;
  margin-bottom: 12px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.project-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
