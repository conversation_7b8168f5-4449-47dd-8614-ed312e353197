<template>
  <div class="home">
    <!-- 歡迎區域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>歡迎使用自動視頻剪輯平台</h1>
            <p>一站式視頻處理解決方案，支持自動剪輯、字幕添加、音頻處理等功能</p>
            <div class="action-buttons">
              <el-button type="primary" size="large" @click="$router.push('/upload')">
                <el-icon><Upload /></el-icon>
                開始上傳
              </el-button>
              <el-button size="large" @click="$router.push('/projects')">
                <el-icon><Folder /></el-icon>
                查看項目
              </el-button>
            </div>
          </div>
          <div class="welcome-image">
            <el-icon class="large-icon"><VideoCamera /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 統計信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon projects">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.projects || 0 }}</div>
                <div class="stat-label">項目總數</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon tasks">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.total || 0 }}</div>
                <div class="stat-label">任務總數</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.processing || 0 }}</div>
                <div class="stat-label">處理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ taskStats.completed || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近項目 -->
    <div class="recent-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>最近項目</span>
            <el-button text @click="$router.push('/projects')">查看全部</el-button>
          </div>
        </template>
        <div v-if="recentProjects.length === 0" class="empty-state">
          <el-empty description="暫無項目" />
        </div>
        <el-row v-else :gutter="20">
          <el-col v-for="project in recentProjects" :key="project.id" :span="8">
            <el-card class="project-card" @click="$router.push(`/projects/${project.id}`)">
              <div class="project-info">
                <h3>{{ project.name }}</h3>
                <p>{{ project.description || '無描述' }}</p>
                <div class="project-meta">
                  <span>{{ formatDate(project.created_at) }}</span>
                  <el-tag :type="getStatusType(project.status)" size="small">
                    {{ getStatusText(project.status) }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'
import { Upload, Folder, VideoCamera, Setting, Loading, Check } from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    Upload,
    Folder,
    VideoCamera,
    Setting,
    Loading,
    Check
  },
  setup() {
    const projectsStore = useProjectsStore()
    const tasksStore = useTasksStore()
    
    const stats = ref({})
    const taskStats = ref({})
    const recentProjects = ref([])

    const loadData = async () => {
      try {
        // 加載項目數據
        const projectsResponse = await projectsStore.fetchProjects({ limit: 6 })
        recentProjects.value = projectsResponse.projects || []
        stats.value = { projects: projectsResponse.total || 0 }

        // 加載任務統計
        const taskStatsResponse = await tasksStore.fetchStats()
        taskStats.value = taskStatsResponse
      } catch (error) {
        console.error('Failed to load data:', error)
      }
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-TW')
    }

    const getStatusType = (status) => {
      const statusMap = {
        'active': 'success',
        'completed': 'info',
        'archived': 'warning'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '進行中',
        'completed': '已完成',
        'archived': '已歸檔'
      }
      return statusMap[status] || status
    }

    onMounted(() => {
      loadData()
    })

    return {
      stats,
      taskStats,
      recentProjects,
      formatDate,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-card :deep(.el-card__body) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.welcome-text h1 {
  font-size: 32px;
  margin-bottom: 16px;
  font-weight: 600;
}

.welcome-text p {
  font-size: 16px;
  margin-bottom: 24px;
  opacity: 0.9;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.large-icon {
  font-size: 120px;
  opacity: 0.3;
}

.stats-section {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.projects {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-card {
  cursor: pointer;
  transition: transform 0.2s;
  margin-bottom: 16px;
}

.project-card:hover {
  transform: translateY(-2px);
}

.project-info h3 {
  margin-bottom: 8px;
  color: #303133;
}

.project-info p {
  color: #606266;
  margin-bottom: 12px;
  font-size: 14px;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 40px;
}
</style>
