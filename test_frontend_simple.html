<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Frontend API Test</h1>
    <button onclick="testCreateProject()">Test Create Project</button>
    <div id="result"></div>

    <script>
        async function testCreateProject() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await axios.post('http://localhost:3000/api/projects/', {
                    name: 'Simple Test Project',
                    description: 'Testing from simple HTML',
                    status: 'active'
                });
                
                resultDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>Status: ${response.status}</p>
                    <p>Response: ${JSON.stringify(response.data, null, 2)}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>Error: ${error.message}</p>
                    <p>Response: ${error.response ? JSON.stringify(error.response.data, null, 2) : 'No response'}</p>
                `;
            }
        }
    </script>
</body>
</html>
