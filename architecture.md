# 自動剪輯平台架構說明文檔

## 1. 系統概述

本系統是一個自動視頻剪輯平台，提供視頻上傳、編輯和處理功能。系統支持多種媒體格式（如MOV、SRT、WAV等），並提供REST API接口以便與n8n等自動化工具集成。

### 1.1 主要功能

- 文件上傳與管理
- 視頻剪輯與處理
- 字幕處理與特效（卡拉OK、打字機、發光等）
- 音頻處理與背景音樂添加
- 片頭片尾添加
- 任務進度跟踪
- 前端可視化操作界面

### 1.2 技術選型

#### 後端
- **語言環境**: Python 3.x + venv
- **Web框架**: FastAPI
- **數據庫**: SQLite
- **任務隊列**: <PERSON><PERSON> (SQLite後端)
- **視頻處理**: MoviePy, ffmpeg-python
- **字幕處理**: pysrt, pysubs2

#### 前端
- **框架**: Vue.js/React
- **HTTP客戶端**: Axios
- **UI組件**: Bootstrap/Tailwind CSS

## 2. 系統架構

### 2.1 目錄結構

```
auto_video_editor/
├── backend/                # 後端代碼
│   ├── app/
│   │   ├── api/            # API路由和依賴
│   │   │   ├── routes/     # API端點定義
│   │   │   │   ├── projects.py
│   │   │   │   ├── uploads.py
│   │   │   │   ├── editing.py
│   │   │   │   └── progress.py
│   │   │   └── dependencies.py
│   │   ├── core/           # 核心配置
│   │   │   ├── config.py
│   │   │   └── database.py
│   │   ├── models/         # 數據模型
│   │   │   ├── project.py
│   │   │   └── job.py
│   │   ├── services/       # 業務邏輯服務
│   │   │   ├── video_editor.py
│   │   │   ├── subtitle_processor.py
│   │   │   └── audio_processor.py
│   │   ├── tasks/          # 異步任務
│   │   │   └── editing_tasks.py
│   │   └── main.py         # 應用入口
│   ├── storage/            # 文件存儲
│   │   ├── uploads/        # 上傳文件
│   │   ├── projects/       # 項目文件
│   │   └── outputs/        # 輸出文件
│   └── requirements.txt    # 依賴清單
└── frontend/               # 前端代碼
    ├── public/
    ├── src/
    │   ├── components/     # UI組件
    │   ├── views/          # 頁面視圖
    │   ├── services/       # API服務
    │   └── App.vue         # 主應用
    └── package.json        # 前端依賴
```

### 2.2 組件關係

```
前端 <--> FastAPI後端 <--> 業務服務 <--> 任務隊列 <--> 視頻處理
  |           |              |
  |           |              +---> 數據庫
  |           |
  |           +---> 文件存儲
  |
  +---> 用戶界面
```

## 3. API設計

### 3.1 上傳API

- **端點**: `/api/uploads/`
- **方法**: POST
- **功能**: 上傳多個文件，支持n8n集成
- **參數**: 
  - `files`: 文件列表
  - `project_id`: 項目ID (可選)
- **返回**: 項目ID和文件信息列表

### 3.2 編輯API

- **端點**: `/api/editing/start`
- **方法**: POST
- **功能**: 開始視頻編輯任務
- **參數**: 
  - `config`: 編輯配置（包含項目ID、主視頻路徑等）
- **返回**: 任務ID和狀態

### 3.3 進度API

- **端點**: `/api/progress/{job_id}`
- **方法**: GET
- **功能**: 獲取編輯任務的進度
- **參數**: 
  - `job_id`: 任務ID
- **返回**: 任務狀態、進度百分比和輸出路徑

### 3.4 項目API

- **端點**: `/api/projects/`
- **方法**: GET/POST
- **功能**: 項目管理
- **參數**: 視操作而定
- **返回**: 項目信息

## 4. 核心服務

### 4.1 視頻編輯服務

`VideoEditor` 類負責處理視頻剪輯的核心邏輯，包括：
- 加載和處理主視頻
- 處理音頻（替換、混合、靜音等）
- 處理字幕和特效
- 添加片頭片尾
- 輸出最終視頻

### 4.2 字幕處理服務

`SubtitleProcessor` 類負責處理字幕文件和特效：
- SRT轉ASS格式
- 應用字幕特效（卡拉OK、打字機、發光等）
- 字幕時間軸調整

### 4.3 音頻處理服務

`AudioProcessor` 類負責處理音頻相關功能：
- 背景音樂添加
- 音頻混合
- 音量調整

## 5. 任務處理流程

1. 用戶上傳文件到系統
2. 系統創建項目並存儲文件
3. 用戶配置編輯參數
4. 系統創建編輯任務並加入隊列
5. 後台工作進程處理任務
6. 系統更新任務進度
7. 任務完成後，生成輸出文件
8. 用戶可下載或預覽結果

## 6. 數據模型

### 6.1 項目模型

- `id`: 唯一標識符
- `name`: 項目名稱
- `created_at`: 創建時間
- `files`: 關聯文件列表

### 6.2 任務模型

- `id`: 唯一標識符
- `project_id`: 關聯項目ID
- `status`: 任務狀態（pending, processing, completed, failed）
- `progress`: 進度百分比
- `output_path`: 輸出文件路徑
- `error`: 錯誤信息（如有）

## 7. 部署指南

### 7.1 環境準備

1. 安裝Python 3.x和venv
2. 安裝ffmpeg系統依賴
3. 創建虛擬環境並激活

### 7.2 後端部署

1. 安裝依賴：`pip install -r requirements.txt`
2. 初始化數據庫：`python -m app.core.database`
3. 啟動API服務：`uvicorn app.main:app --host 0.0.0.0 --port 8000`
4. 啟動任務工作進程：`python -m huey.bin.huey_consumer app.tasks.editing_tasks.huey`

### 7.3 前端部署

1. 安裝依賴：`npm install`
2. 構建前端：`npm run build`
3. 部署到Web服務器

### 7.4 生產環境建議

- 使用Gunicorn或Uvicorn作為ASGI服務器
- 使用Nginx作為反向代理
- 使用systemd或supervisor管理進程
- 設置定時任務清理臨時文件

## 8. 擴展與優化

### 8.1 未來功能

- 視頻轉場效果
- 更多字幕特效
- 視頻濾鏡和顏色校正
- 批量處理功能

### 8.2 性能優化

- 文件處理流程優化
- 任務隊列優化
- 緩存機制
- 並行處理

## 9. 與n8n集成

### 9.1 工作流程

1. n8n通過上傳API發送文件
2. 獲取項目ID和文件路徑
3. 通過編輯API提交編輯配置
4. 獲取任務ID
5. 定期通過進度API檢查進度
6. 任務完成後獲取輸出文件路徑
7. 下載或處理輸出文件

### 9.2 示例配置

```json
{
  "project_id": "uuid-here",
  "main_video_path": "storage/uploads/uuid-here/main.mov",
  "subtitle_path": "storage/uploads/uuid-here/subtitles.srt",
  "audio_path": "storage/uploads/uuid-here/background.wav",
  "subtitle_effect": "karaoke",
  "intro_path": "storage/uploads/uuid-here/intro.mp4",
  "outro_path": "storage/uploads/uuid-here/outro.mp4"
}
```

## 10. 故障排除

### 10.1 常見問題

- 文件上傳失敗
- 視頻處理錯誤
- 任務卡住或失敗

### 10.2 日誌與監控

- API服務日誌
- 任務處理日誌
- 系統監控