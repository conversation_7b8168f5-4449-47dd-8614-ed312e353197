#!/usr/bin/env python3
"""Test editing API on port 8001"""

import requests
import os

def test_editing_8001():
    """Test editing API on port 8001"""
    print("🔍 Testing editing API on port 8001...")
    
    # Create test video file
    test_video_path = os.path.join(os.getcwd(), "storage", "test_video.mp4")
    os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
    
    # Create a dummy video file
    with open(test_video_path, 'wb') as f:
        f.write(b'\x00' * 1024)  # 1KB dummy file
    
    print(f"📋 Created test file: {test_video_path}")
    
    # Test data
    editing_data = {
        'project_id': 'b9da6931-653e-4a2c-a575-a26dadeb6c08',
        'main_video_path': test_video_path,
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print(f"📋 Testing with data: {editing_data}")
    
    try:
        response = requests.post('http://localhost:8001/api/editing/start', json=editing_data)
        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        print(f"📋 Response text: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Editing started successfully!")
            print(f"📋 Job ID: {result.get('job_id')}")
        elif response.status_code == 500:
            print("❌ 500 Internal Server Error detected!")
            try:
                error_data = response.json()
                error_detail = error_data.get('detail', 'No detail')
                print(f"📋 Error detail: {error_detail}")
                
                if 'delay' in error_detail:
                    print("🔍 Error mentions 'delay' - this is the issue we're tracking")
                if 'TaskWrapper' in error_detail:
                    print("🔍 Error mentions 'TaskWrapper' - Huey task issue")
            except:
                print("📋 Could not parse error response")
        else:
            print(f"📋 Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")
    
    # Clean up
    if os.path.exists(test_video_path):
        os.remove(test_video_path)
        print(f"🧹 Cleaned up test file: {test_video_path}")

if __name__ == "__main__":
    test_editing_8001()
