#!/usr/bin/env python3
"""Test frontend create project functionality"""

import requests
import time

def test_frontend_create():
    """Test frontend create project functionality"""
    print("🔍 Testing frontend after API fix...")
    
    # Test the API directly first
    print("\n1. Testing backend API directly:")
    project_data = {
        'name': 'Frontend Test Project',
        'description': 'Testing frontend integration',
        'status': 'active'
    }
    
    try:
        response = requests.post('http://localhost:8000/api/projects/', json=project_data)
        print(f"📋 Backend API status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📋 Backend response structure:")
            print(f"   - message: {result.get('message')}")
            print(f"   - project_id: {result.get('project_id')}")
            print(f"   - project: {result.get('project', {}).get('name') if result.get('project') else 'None'}")
        else:
            print(f"❌ Backend API failed: {response.text}")
    except Exception as e:
        print(f"❌ Backend API error: {e}")
    
    # Test frontend proxy
    print("\n2. Testing frontend proxy:")
    try:
        response = requests.post('http://localhost:3000/api/projects/', json=project_data)
        print(f"📋 Frontend proxy status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📋 Frontend proxy response structure:")
            print(f"   - message: {result.get('message')}")
            print(f"   - project_id: {result.get('project_id')}")
            print(f"   - project: {result.get('project', {}).get('name') if result.get('project') else 'None'}")
        else:
            print(f"❌ Frontend proxy failed: {response.text}")
    except Exception as e:
        print(f"❌ Frontend proxy error: {e}")
    
    print("\n3. Checking frontend logs...")
    print("請檢查瀏覽器控制台是否有 JavaScript 錯誤")
    print("請嘗試點擊「新增項目」按鈕，看看是否有錯誤信息")

if __name__ == "__main__":
    test_frontend_create()
