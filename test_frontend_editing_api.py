#!/usr/bin/env python3
"""
測試前端編輯API調用 - 模擬前端請求
"""

import requests
import json
import time

def test_frontend_editing_api():
    """測試前端編輯API調用"""
    base_url = 'http://localhost:8000'
    project_id = 'e26a251a-0e00-4db7-a3fc-24b0341e2277'
    
    print("🌐 測試前端編輯API調用...")
    
    # 1. 獲取項目詳情（模擬前端加載項目頁面）
    print("\n1️⃣ 獲取項目詳情...")
    response = requests.get(f'{base_url}/api/projects/{project_id}')
    if response.status_code == 200:
        project = response.json()
        print(f"✅ 項目: {project['name']}")
    else:
        print(f"❌ 無法獲取項目: {response.status_code}")
        return
    
    # 2. 獲取項目文件（模擬前端加載文件列表）
    print("\n2️⃣ 獲取項目文件...")
    response = requests.get(f'{base_url}/api/projects/{project_id}/files')
    if response.status_code == 200:
        files = response.json()
        print(f"✅ 找到 {len(files)} 個文件")
        video_files = [f for f in files if f['filename'].lower().endswith(('.mov', '.mp4', '.avi'))]
        if video_files:
            main_video = video_files[0]['filename']
            print(f"📹 主視頻文件: {main_video}")
        else:
            print("❌ 沒有找到視頻文件")
            return
    else:
        print(f"❌ 無法獲取文件列表: {response.status_code}")
        return
    
    # 3. 獲取現有任務（模擬前端檢查任務狀態）
    print("\n3️⃣ 檢查現有任務...")
    response = requests.get(f'{base_url}/api/progress/?project_id={project_id}')
    if response.status_code == 200:
        tasks = response.json()
        print(f"✅ 找到 {len(tasks)} 個任務")
        if isinstance(tasks, list) and len(tasks) > 0:
            recent_tasks = tasks[-3:] if len(tasks) >= 3 else tasks
            for task in recent_tasks:  # 顯示最近3個任務
                print(f"   - {task['job_id']}: {task['status']} ({task['progress']}%)")
        else:
            print("   沒有任務記錄")
    else:
        print(f"❌ 無法獲取任務列表: {response.status_code}")
    
    # 4. 模擬前端開始編輯（使用前端會發送的配置）
    print("\n4️⃣ 模擬前端開始編輯...")
    
    # 這是前端會發送的典型配置
    frontend_config = {
        'project_id': project_id,
        'main_video_filename': main_video,
        'output_size': '1080x1920',  # 豎屏格式
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0,
        'intro_text': '',
        'outro_text': '',
        'background_music': ''
    }
    
    print("📝 前端編輯配置:")
    print(json.dumps(frontend_config, indent=2, ensure_ascii=False))
    
    # 發送編輯請求
    print("\n🚀 發送編輯請求...")
    response = requests.post(f'{base_url}/api/editing/start', json=frontend_config)
    
    if response.status_code == 200:
        result = response.json()
        job_id = result.get('job_id')
        print(f"✅ 編輯任務已啟動，任務ID: {job_id}")
        
        # 5. 模擬前端監控進度
        print("\n5️⃣ 模擬前端監控進度...")
        monitor_like_frontend(base_url, job_id, project_id)
        
    else:
        print(f"❌ 編輯啟動失敗:")
        print(f"   狀態碼: {response.status_code}")
        print(f"   錯誤信息: {response.text}")
        
        # 檢查詳細錯誤
        try:
            error_data = response.json()
            print(f"   詳細錯誤: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
        except:
            pass

def monitor_like_frontend(base_url, job_id, project_id):
    """模擬前端監控進度的方式"""
    print(f"📊 監控任務進度: {job_id}")
    
    for i in range(1, 8):  # 前端通常檢查7-8次
        try:
            # 前端會同時檢查單個任務和項目任務列表
            task_response = requests.get(f'{base_url}/api/progress/{job_id}')
            project_tasks_response = requests.get(f'{base_url}/api/progress/?project_id={project_id}')
            
            if task_response.status_code == 200:
                data = task_response.json()
                status = data.get('status', 'unknown')
                progress = data.get('progress', 0)
                
                print(f"第{i}次檢查 - 狀態: {status}, 進度: {progress}%")
                
                if status == 'completed':
                    output_path = data.get('output_path', 'N/A')
                    print(f"🎉 編輯完成! 輸出文件: {output_path}")
                    
                    # 檢查輸出文件是否存在
                    import os
                    full_path = os.path.join('backend', output_path)
                    if os.path.exists(full_path):
                        file_size = os.path.getsize(full_path)
                        print(f"📁 文件確認存在，大小: {file_size:,} bytes")
                    else:
                        print(f"⚠️ 輸出文件不存在: {full_path}")
                    break
                    
                elif status == 'failed':
                    error = data.get('error', 'Unknown error')
                    print(f"💥 編輯失敗: {error}")
                    break
                elif status == 'cancelled':
                    print(f"⏹️ 編輯已取消")
                    break
            else:
                print(f"⚠️ 無法獲取進度: {task_response.status_code}")
        
        except Exception as e:
            print(f"❌ 檢查進度時出錯: {e}")
        
        if i < 7:
            print("   等待3秒...")
            time.sleep(3)
    
    print("📊 進度監控結束")

if __name__ == "__main__":
    test_frontend_editing_api()
