@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 系統測試

echo ========================================
echo 🧪 自動視頻剪輯平台 - 系統測試
echo ========================================
echo.

:: 檢查虛擬環境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虛擬環境不存在，請先運行 setup_windows.bat
    pause
    exit /b 1
)

:: 激活虛擬環境
call venv\Scripts\activate.bat

:: 檢查測試腳本
if not exist "test_system.py" (
    echo ❌ 測試腳本不存在
    pause
    exit /b 1
)

echo 🔍 開始系統測試...
echo.

:: 運行 Python 測試腳本
python test_system.py

echo.
echo 📋 測試完成！
echo.

:: 詢問是否運行額外測試
set /p run_extra=是否運行額外的組件測試？ (y/n): 
if /i "%run_extra%"=="y" (
    echo.
    echo 🔧 運行後端單元測試...
    cd backend
    if exist "tests" (
        python -m pytest tests/ -v
    ) else (
        echo ⚠️  後端測試目錄不存在，跳過單元測試
    )
    cd ..
    
    echo.
    echo 🔧 運行前端測試...
    cd frontend
    if exist "tests" (
        npm run test
    ) else (
        echo ⚠️  前端測試配置不存在，跳過前端測試
    )
    cd ..
)

echo.
echo 🔍 檢查系統健康狀態...

:: 檢查存儲目錄
echo 📁 檢查存儲目錄...
if exist "storage" (
    echo ✅ storage 目錄存在
    if exist "storage\uploads" echo ✅ uploads 目錄存在
    if exist "storage\projects" echo ✅ projects 目錄存在
    if exist "storage\outputs" echo ✅ outputs 目錄存在
    if exist "storage\temp" echo ✅ temp 目錄存在
) else (
    echo ❌ storage 目錄不存在
)

:: 檢查數據庫
echo 🗄️  檢查數據庫...
if exist "storage\app.db" (
    echo ✅ 數據庫文件存在
) else (
    echo ⚠️  數據庫文件不存在，可能需要初始化
)

:: 檢查配置文件
echo ⚙️  檢查配置文件...
if exist "backend\.env" (
    echo ✅ 後端配置文件存在
) else (
    echo ⚠️  後端配置文件不存在
)

:: 檢查依賴
echo 📦 檢查 Python 依賴...
pip list | find "fastapi" >nul
if not errorlevel 1 (
    echo ✅ FastAPI 已安裝
) else (
    echo ❌ FastAPI 未安裝
)

pip list | find "uvicorn" >nul
if not errorlevel 1 (
    echo ✅ Uvicorn 已安裝
) else (
    echo ❌ Uvicorn 未安裝
)

pip list | find "huey" >nul
if not errorlevel 1 (
    echo ✅ Huey 已安裝
) else (
    echo ❌ Huey 未安裝
)

echo.
echo 📦 檢查 Node.js 依賴...
cd frontend
if exist "node_modules\vue" (
    echo ✅ Vue.js 已安裝
) else (
    echo ❌ Vue.js 未安裝
)

if exist "node_modules\element-plus" (
    echo ✅ Element Plus 已安裝
) else (
    echo ❌ Element Plus 未安裝
)

if exist "node_modules\vite" (
    echo ✅ Vite 已安裝
) else (
    echo ❌ Vite 未安裝
)
cd ..

:: 檢查可選依賴
echo.
echo 🔍 檢查可選依賴...
ffmpeg -version >nul 2>&1
if not errorlevel 1 (
    echo ✅ FFmpeg 已安裝
) else (
    echo ⚠️  FFmpeg 未安裝（建議安裝以獲得更好性能）
)

echo.
echo 🎉 系統檢查完成！
echo.
echo 📋 測試總結：
echo - 如果所有核心測試都通過，系統可以正常使用
echo - 如果有測試失敗，請檢查相應的配置和依賴
echo - 建議安裝 FFmpeg 以獲得更好的視頻處理性能
echo.
echo 💡 下一步：
echo - 運行 start_windows.bat 啟動系統
echo - 訪問 http://localhost:3000 使用應用
echo.
pause
