import axios from 'axios'
import { ElMessage } from 'element-plus'

// 創建 axios 實例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 請求攔截器
api.interceptors.request.use(
  config => {
    // 可以在這裡添加認證 token
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 響應攔截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const message = error.response?.data?.detail || error.message || '請求失敗'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 項目相關 API
export const projectsAPI = {
  // 獲取項目列表
  getProjects(params = {}) {
    return api.get('/projects/', { params })
  },

  // 創建項目
  async createProject(data) {
    const response = await api.post('/projects/', data)
    // 後端返回 { message, project_id, project }，我們需要返回 project
    return response.project
  },

  // 獲取項目詳情
  getProject(projectId) {
    return api.get(`/projects/${projectId}`)
  },

  // 更新項目
  updateProject(projectId, data) {
    return api.put(`/projects/${projectId}`, data)
  },

  // 刪除項目
  deleteProject(projectId) {
    return api.delete(`/projects/${projectId}`)
  },

  // 獲取項目文件
  getProjectFiles(projectId) {
    return api.get(`/projects/${projectId}/files`)
  },

  // 獲取項目統計
  getProjectStats(projectId) {
    return api.get(`/projects/${projectId}/stats`)
  }
}

// 文件上傳相關 API
export const uploadsAPI = {
  // 上傳文件
  uploadFiles(formData, onProgress) {
    return api.post('/uploads/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },

  // 獲取上傳進度
  getUploadProgress(uploadId) {
    return api.get(`/uploads/progress/${uploadId}`)
  }
}

// 編輯相關 API
export const editingAPI = {
  // 開始編輯任務
  startEditing(data) {
    return api.post('/editing/start', data)
  },

  // 取消任務
  cancelTask(jobId) {
    return api.post(`/editing/cancel/${jobId}`)
  },

  // 重試任務
  retryTask(jobId) {
    return api.post(`/editing/retry/${jobId}`)
  }
}

// 進度相關 API
export const progressAPI = {
  // 獲取任務進度
  getTaskProgress(jobId) {
    return api.get(`/progress/${jobId}`)
  },

  // 獲取任務列表
  getTasks(params = {}) {
    return api.get('/progress/', { params })
  },

  // 獲取統計信息
  getStats() {
    return api.get('/progress/stats/summary')
  }
}

// 健康檢查
export const healthAPI = {
  check() {
    return api.get('/health')
  }
}

export default api
