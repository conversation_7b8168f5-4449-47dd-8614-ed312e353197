# 自動視頻剪輯平台

一個基於 FastAPI 和 Vue.js 的全棧自動視頻剪輯平台，支持視頻上傳、自動剪輯、字幕處理、音頻編輯等功能。

## 🌟 功能特色

- **視頻處理**: 支持多種視頻格式的上傳、剪輯、調整大小
- **字幕處理**: 支持 SRT、ASS、VTT 格式，提供多種字幕效果
- **音頻處理**: 背景音樂添加、音量控制、音頻替換
- **任務隊列**: 後台異步處理，實時進度追蹤
- **項目管理**: 完整的項目生命週期管理
- **現代化 UI**: 基於 Element Plus 的響應式界面
- **API 文檔**: 自動生成的 OpenAPI 文檔

## 🏗️ 系統架構

```
自動視頻剪輯平台/
├── backend/                 # FastAPI 後端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 數據模型
│   │   ├── services/       # 業務邏輯
│   │   └── tasks/          # 後台任務
│   └── requirements.txt    # Python 依賴
├── frontend/               # Vue.js 前端
│   ├── src/
│   │   ├── components/     # Vue 組件
│   │   ├── views/          # 頁面視圖
│   │   ├── stores/         # Pinia 狀態管理
│   │   └── services/       # API 服務
│   └── package.json        # Node.js 依賴
├── storage/                # 文件存儲
│   ├── uploads/            # 上傳文件
│   ├── projects/           # 項目文件
│   └── outputs/            # 輸出文件
└── architecture.md         # 架構文檔
```

## 🚀 快速開始

### Windows 用戶（推薦）

#### 一鍵安裝和啟動
```cmd
# 克隆項目
git clone <repository-url>
cd auto-video-editor

# 1. 環境設置（僅需運行一次）
setup_windows.bat

# 2. 啟動系統
start_windows.bat
```

詳細的 Windows 安裝指南請參考：[WINDOWS_SETUP.md](WINDOWS_SETUP.md)

### 跨平台方法

#### 方法一：Python 自動啟動
```bash
# 克隆項目
git clone <repository-url>
cd auto-video-editor

# 運行自動啟動腳本
python start_system.py
```

#### 方法二：手動啟動

#### 1. 環境要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

#### 2. 後端設置

```bash
# 進入後端目錄
cd backend

# 安裝依賴
pip install -r requirements.txt

# 初始化數據庫
python -c "from app.core.database import init_db; init_db()"

# 啟動後端服務
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 啟動任務隊列（新終端）
huey_consumer app.tasks.editing_tasks.huey
```

#### 3. 前端設置

```bash
# 進入前端目錄
cd frontend

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev
```

### 3. 訪問應用

- **前端界面**: http://localhost:3000
- **後端 API**: http://localhost:8000
- **API 文檔**: http://localhost:8000/docs

## 📖 使用指南

### 1. 創建項目

1. 訪問前端界面
2. 點擊「新建項目」
3. 填寫項目名稱和描述
4. 點擊「創建」

### 2. 上傳文件

1. 進入項目詳情頁面
2. 點擊「上傳文件」
3. 拖拽或選擇視頻、音頻、字幕文件
4. 配置項目設置
5. 點擊「開始上傳」

### 3. 開始編輯

1. 上傳完成後，點擊「開始編輯」
2. 配置編輯參數：
   - 字幕效果（無效果、卡拉OK、打字機、發光、陰影）
   - 背景音樂音量
   - 音頻音量
   - 輸出尺寸
3. 點擊「開始處理」

### 4. 監控進度

1. 訪問「任務監控」頁面
2. 查看實時處理進度
3. 下載完成的視頻

## 🔧 配置說明

### 後端配置

編輯 `backend/app/core/config.py`：

```python
class Settings(BaseSettings):
    app_name: str = "Auto Video Editor"
    database_url: str = "sqlite:///./storage/app.db"
    upload_dir: str = "storage/uploads"
    project_dir: str = "storage/projects"
    output_dir: str = "storage/outputs"
    max_file_size: int = 500 * 1024 * 1024  # 500MB
    allowed_video_formats: list = [".mp4", ".avi", ".mov", ".mkv"]
    allowed_audio_formats: list = [".mp3", ".wav", ".aac", ".flac"]
    allowed_subtitle_formats: list = [".srt", ".ass", ".vtt"]
```

### 前端配置

編輯 `frontend/src/services/api.js` 中的 API 基礎 URL：

```javascript
const API_BASE_URL = 'http://localhost:8000/api'
```

## 🧪 測試

### 運行系統測試

```bash
python test_system.py
```

### 運行單元測試

```bash
# 後端測試
cd backend
python -m pytest

# 前端測試
cd frontend
npm run test
```

## 📚 API 文檔

啟動後端服務後，訪問 http://localhost:8000/docs 查看完整的 API 文檔。

### 主要 API 端點

- `GET /api/projects/` - 獲取項目列表
- `POST /api/projects/` - 創建新項目
- `GET /api/projects/{id}` - 獲取項目詳情
- `POST /api/uploads/` - 上傳文件
- `POST /api/editing/start` - 開始編輯任務
- `GET /api/progress/tasks` - 獲取任務列表
- `GET /api/progress/stats` - 獲取統計信息

## 🔌 n8n 集成

系統支持與 n8n 工作流平台集成，可以通過 webhook 觸發自動化視頻處理流程。

### 配置 n8n Webhook

1. 在 n8n 中創建新的工作流
2. 添加 Webhook 節點
3. 配置 Webhook URL: `http://localhost:8000/api/webhooks/n8n`
4. 設置請求方法為 POST
5. 配置請求體包含項目信息和處理參數

## 🛠️ 開發指南

### 添加新的字幕效果

1. 在 `backend/app/services/subtitle_processor.py` 中添加新效果
2. 更新前端 `frontend/src/views/Upload.vue` 中的選項

### 添加新的視頻處理功能

1. 在 `backend/app/services/video_editor.py` 中實現功能
2. 在 `backend/app/api/editing.py` 中添加 API 端點
3. 更新前端相應的服務和組件

## 🐛 故障排除

### 常見問題

1. **後端啟動失敗**
   - 檢查 Python 版本是否為 3.8+
   - 確保所有依賴已正確安裝
   - 檢查端口 8000 是否被占用

2. **前端啟動失敗**
   - 檢查 Node.js 版本是否為 16+
   - 刪除 `node_modules` 重新安裝依賴
   - 檢查端口 3000 是否被占用

3. **文件上傳失敗**
   - 檢查文件大小是否超過限制
   - 確保文件格式受支持
   - 檢查存儲目錄權限

4. **視頻處理失敗**
   - 確保 ffmpeg 已正確安裝
   - 檢查視頻文件是否損壞
   - 查看任務隊列日誌

### 日誌查看

- 後端日誌：控制台輸出
- 任務隊列日誌：Huey 控制台輸出
- 前端日誌：瀏覽器開發者工具

## 📄 許可證

MIT License

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📞 支持

如有問題，請提交 Issue 或聯繫開發團隊。
