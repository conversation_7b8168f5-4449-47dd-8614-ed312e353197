import uuid
import json
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from app.core.database import db

class ProjectFile(BaseModel):
    """項目文件模型"""
    id: Optional[int] = None
    project_id: str
    filename: str
    file_path: str
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    created_at: Optional[datetime] = None

class Project(BaseModel):
    """項目模型"""
    id: str
    name: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    status: str = "active"
    description: Optional[str] = None

class ProjectCreate(BaseModel):
    """創建項目的請求模型"""
    name: str
    description: Optional[str] = None

class ProjectResponse(BaseModel):
    """項目響應模型"""
    id: str
    name: str
    created_at: datetime
    updated_at: datetime
    status: str
    description: Optional[str] = None
    files: List[ProjectFile] = []

def create_project(project_data: ProjectCreate) -> str:
    """創建新項目"""
    project_id = str(uuid.uuid4())
    
    query = """
    INSERT INTO projects (id, name, description, created_at, updated_at)
    VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    """
    
    db.execute_insert(query, (project_id, project_data.name, project_data.description))
    return project_id

def get_project(project_id: str) -> Optional[Dict[str, Any]]:
    """根據ID獲取項目"""
    query = "SELECT * FROM projects WHERE id = ?"
    results = db.execute_query(query, (project_id,))
    
    if not results:
        return None
    
    project = results[0]
    
    # 獲取項目文件
    files_query = "SELECT * FROM project_files WHERE project_id = ?"
    files = db.execute_query(files_query, (project_id,))
    
    project['files'] = files
    return project

def get_all_projects() -> List[Dict[str, Any]]:
    """獲取所有項目"""
    query = "SELECT * FROM projects ORDER BY created_at DESC"
    projects = db.execute_query(query)
    
    # 為每個項目獲取文件列表
    for project in projects:
        files_query = "SELECT * FROM project_files WHERE project_id = ?"
        files = db.execute_query(files_query, (project['id'],))
        project['files'] = files
    
    return projects

def update_project(project_id: str, update_data: Dict[str, Any]) -> bool:
    """更新項目"""
    # 構建動態更新查詢
    set_clauses = []
    params = []
    
    for key, value in update_data.items():
        if key in ['name', 'description', 'status']:
            set_clauses.append(f"{key} = ?")
            params.append(value)
    
    if not set_clauses:
        return False
    
    set_clauses.append("updated_at = CURRENT_TIMESTAMP")
    params.append(project_id)
    
    query = f"UPDATE projects SET {', '.join(set_clauses)} WHERE id = ?"
    
    rows_affected = db.execute_update(query, tuple(params))
    return rows_affected > 0

def delete_project(project_id: str) -> bool:
    """刪除項目（軟刪除）"""
    query = "UPDATE projects SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    rows_affected = db.execute_update(query, (project_id,))
    return rows_affected > 0

def add_project_file(project_id: str, file_info: Dict[str, Any]) -> int:
    """為項目添加文件"""
    query = """
    INSERT INTO project_files (project_id, filename, file_path, file_type, file_size, content_type)
    VALUES (?, ?, ?, ?, ?, ?)
    """
    
    params = (
        project_id,
        file_info.get('filename'),
        file_info.get('file_path'),
        file_info.get('file_type'),
        file_info.get('file_size'),
        file_info.get('content_type')
    )
    
    return db.execute_insert(query, params)

def get_project_files(project_id: str) -> List[Dict[str, Any]]:
    """獲取項目的所有文件"""
    query = "SELECT * FROM project_files WHERE project_id = ? ORDER BY created_at"
    return db.execute_query(query, (project_id,))

def remove_project_file(file_id: int) -> bool:
    """刪除項目文件記錄"""
    query = "DELETE FROM project_files WHERE id = ?"
    rows_affected = db.execute_update(query, (file_id,))
    return rows_affected > 0
