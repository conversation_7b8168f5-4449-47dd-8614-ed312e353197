#!/usr/bin/env python3
"""Test task directly"""

import sys
import os
sys.path.append('backend')

from app.tasks.editing_tasks import process_video_task
import uuid

def test_task_direct():
    """Test task directly"""
    print("🔍 Testing task directly...")
    
    # Create a dummy video file for testing
    test_video_path = os.path.abspath("storage/test_video.mp4")
    os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
    
    # Create a dummy file
    with open(test_video_path, "w") as f:
        f.write("dummy video content")
    
    print(f"📋 Created test file: {test_video_path}")
    
    # Test task attributes
    print(f"📋 Task type: {type(process_video_task)}")
    print(f"📋 Task attributes: {dir(process_video_task)}")
    
    # Test different calling methods
    job_id = str(uuid.uuid4())
    project_id = "b9da6931-653e-4a2c-a575-a26dadeb6c08"
    config = {
        'project_id': project_id,
        'main_video_path': test_video_path,
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    try:
        print("📋 Testing .schedule() method...")
        result = process_video_task.schedule(args=(job_id, test_video_path, config), convert_utc=False)
        print(f"✅ .schedule() method works: {result}")
    except Exception as e:
        print(f"❌ .schedule() method failed: {e}")
    
    try:
        print("📋 Testing direct call...")
        result = process_video_task(job_id, test_video_path, config)
        print(f"✅ Direct call works: {result}")
    except Exception as e:
        print(f"❌ Direct call failed: {e}")
    
    # Clean up
    if os.path.exists(test_video_path):
        os.remove(test_video_path)
        print(f"🧹 Cleaned up test file: {test_video_path}")

if __name__ == "__main__":
    test_task_direct()
