#!/usr/bin/env python3
"""簡單測試文件刪除 API"""

import sys
import os
import requests
sys.path.append('backend')

from backend.app.core.database import get_db

def create_test_file_record():
    """直接在數據庫中創建測試文件記錄"""
    print("=== 創建測試文件記錄 ===")
    
    db = get_db()
    
    # 創建測試文件
    test_file_path = "storage/uploads/test_file.txt"
    os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write("This is a test file for deletion")
    
    # 插入文件記錄
    query = """
    INSERT INTO project_files (filename, file_path, file_size, file_type, project_id)
    VALUES (?, ?, ?, ?, ?)
    """

    file_size = os.path.getsize(test_file_path)
    params = ("test_file.txt", test_file_path, file_size, "text/plain", "test-project-id")
    
    try:
        db.execute_update(query, params)
        print(f"✓ 創建測試文件記錄成功: {test_file_path}")
        
        # 獲取插入的文件 ID
        files = db.execute_query("SELECT id FROM project_files WHERE filename = ?", ("test_file.txt",))
        if files:
            return files[0]['id']
        else:
            print("✗ 無法獲取文件 ID")
            return None
    except Exception as e:
        print(f"✗ 創建測試文件記錄失敗: {e}")
        return None

def test_delete_api(file_id):
    """測試刪除 API"""
    print(f"\n=== 測試刪除 API (文件 ID: {file_id}) ===")
    
    try:
        response = requests.delete(f"http://localhost:8000/api/files/{file_id}")
        print(f"響應狀態碼: {response.status_code}")
        print(f"響應內容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 刪除 API 調用成功")
            return True
        else:
            print("✗ 刪除 API 調用失敗")
            return False
    except Exception as e:
        print(f"✗ 刪除 API 調用異常: {e}")
        return False

def check_file_status(file_id):
    """檢查文件狀態"""
    print(f"\n=== 檢查文件狀態 (ID: {file_id}) ===")
    
    db = get_db()
    
    # 檢查數據庫記錄
    files = db.execute_query("SELECT * FROM project_files WHERE id = ?", (file_id,))
    if files:
        file_info = files[0]
        print(f"數據庫記錄: 存在 - {file_info['filename']}")
        
        # 檢查物理文件
        if os.path.exists(file_info['file_path']):
            print(f"物理文件: 存在 - {file_info['file_path']}")
        else:
            print(f"物理文件: 不存在 - {file_info['file_path']}")
    else:
        print("數據庫記錄: 不存在")

def main():
    print("=== 簡單文件刪除 API 測試 ===")
    
    # 創建測試文件記錄
    file_id = create_test_file_record()
    if not file_id:
        print("無法創建測試文件記錄，退出測試")
        return
    
    # 檢查創建後狀態
    print("\n=== 創建後狀態 ===")
    check_file_status(file_id)
    
    # 測試刪除 API
    success = test_delete_api(file_id)
    
    # 檢查刪除後狀態
    print("\n=== 刪除後狀態 ===")
    check_file_status(file_id)
    
    if success:
        print("\n🎉 文件刪除 API 測試成功！")
    else:
        print("\n❌ 文件刪除 API 測試失敗！")

if __name__ == "__main__":
    main()
