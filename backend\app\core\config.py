import os
from typing import Optional
from pydantic import BaseSettings

class Settings(BaseSettings):
    """應用配置設置"""
    
    # 應用基本設置
    app_name: str = "Auto Video Editor"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 數據庫設置
    database_url: str = "sqlite:///./storage/app.db"
    
    # 文件存儲設置
    upload_dir: str = "storage/uploads"
    project_dir: str = "storage/projects"
    output_dir: str = "storage/outputs"
    max_file_size: int = 500 * 1024 * 1024  # 500MB
    
    # FFmpeg 設置
    ffmpeg_path: Optional[str] = None
    ffprobe_path: Optional[str] = None
    
    # 視頻處理設置
    default_video_codec: str = "libx264"
    default_audio_codec: str = "aac"
    default_video_bitrate: str = "2M"
    default_audio_bitrate: str = "128k"
    
    # 字幕設置
    default_subtitle_font: str = "Arial"
    default_subtitle_size: int = 24
    default_subtitle_color: str = "white"
    
    # 任務隊列設置
    huey_db_path: str = "storage/huey.db"
    max_concurrent_tasks: int = 2
    
    # API 設置
    api_prefix: str = "/api"
    cors_origins: list = ["*"]
    
    # 安全設置
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 創建全局設置實例
settings = Settings()

# 確保必要的目錄存在
def ensure_directories():
    """確保所有必要的目錄都存在"""
    directories = [
        settings.upload_dir,
        settings.project_dir,
        settings.output_dir,
        os.path.dirname(settings.huey_db_path),
        os.path.dirname(settings.database_url.replace("sqlite:///", ""))
    ]
    
    for directory in directories:
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"Created directory: {directory}")

# 初始化時創建目錄
ensure_directories()
