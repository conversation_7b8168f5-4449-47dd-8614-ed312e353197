from fastapi import APIRouter, HTTPException
from app.models.job import get_job

router = APIRouter()

@router.get("/{job_id}")
async def get_progress(job_id: str):
    """獲取編輯任務的進度"""
    job = get_job(job_id)
    
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return {
        "job_id": job_id,
        "status": job["status"],
        "progress": job["progress"],
        "output_path": job.get("output_path"),
        "error": job.get("error")
    }