from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from app.models.job import (
    get_job, get_all_jobs, get_jobs_by_project,
    get_pending_jobs, get_processing_jobs, get_completed_jobs, get_failed_jobs
)
from app.api.dependencies import validate_job_exists

router = APIRouter()

@router.get("/{job_id}")
async def get_job_progress(job_id: str):
    """獲取編輯任務的進度"""
    try:
        job = get_job(job_id)

        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        return {
            "job_id": job_id,
            "project_id": job["project_id"],
            "status": job["status"],
            "progress": job["progress"],
            "output_path": job.get("output_path"),
            "error": job.get("error"),
            "created_at": job.get("created_at"),
            "updated_at": job.get("updated_at"),
            "config": job.get("config")
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job progress: {str(e)}")

@router.get("/")
async def list_jobs(
    status: Optional[str] = Query(None, description="Filter by job status"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    limit: Optional[int] = Query(None, description="Limit number of results"),
    offset: Optional[int] = Query(0, description="Offset for pagination")
):
    """獲取任務列表"""
    try:
        if project_id:
            jobs = get_jobs_by_project(project_id)
        elif status:
            if status == "pending":
                jobs = get_pending_jobs()
            elif status == "processing":
                jobs = get_processing_jobs()
            elif status == "completed":
                jobs = get_completed_jobs()
            elif status == "failed":
                jobs = get_failed_jobs()
            else:
                jobs = get_all_jobs(status=status)
        else:
            jobs = get_all_jobs()

        # 分頁
        if limit:
            jobs = jobs[offset:offset + limit]
        elif offset:
            jobs = jobs[offset:]

        return {
            "jobs": jobs,
            "total": len(jobs),
            "offset": offset,
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list jobs: {str(e)}")

@router.get("/stats/summary")
async def get_job_statistics():
    """獲取任務統計信息"""
    try:
        all_jobs = get_all_jobs()

        stats = {
            "total_jobs": len(all_jobs),
            "status_counts": {
                "pending": 0,
                "processing": 0,
                "completed": 0,
                "failed": 0,
                "cancelled": 0
            },
            "recent_jobs": []
        }

        # 統計各狀態的任務數量
        for job in all_jobs:
            status = job.get('status', 'unknown')
            if status in stats["status_counts"]:
                stats["status_counts"][status] += 1
            else:
                stats["status_counts"][status] = stats["status_counts"].get(status, 0) + 1

        # 獲取最近的10個任務
        stats["recent_jobs"] = all_jobs[:10]

        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job statistics: {str(e)}")

@router.get("/{job_id}/logs")
async def get_job_logs(
    job_id: str,
    job: dict = Depends(validate_job_exists)
):
    """獲取任務日誌（如果有的話）"""
    try:
        # 這裡可以實現日誌功能，目前返回基本信息
        return {
            "job_id": job_id,
            "logs": [
                {
                    "timestamp": job.get("created_at"),
                    "level": "INFO",
                    "message": f"Job created with status: {job.get('status')}"
                },
                {
                    "timestamp": job.get("updated_at"),
                    "level": "INFO",
                    "message": f"Job status updated to: {job.get('status')}"
                }
            ],
            "error": job.get("error")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job logs: {str(e)}")