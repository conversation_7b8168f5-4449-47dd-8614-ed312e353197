auto_video_editor/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   ├── routes/
│   │   │   │   ├── projects.py
│   │   │   │   ├── uploads.py
│   │   │   │   ├── editing.py
│   │   │   │   └── progress.py
│   │   │   └── dependencies.py
│   │   ├── core/
│   │   │   ├── config.py
│   │   │   └── database.py
│   │   ├── models/
│   │   │   ├── project.py
│   │   │   └── job.py
│   │   ├── services/
│   │   │   ├── video_editor.py
│   │   │   ├── subtitle_processor.py
│   │   │   └── audio_processor.py
│   │   ├── tasks/
│   │   │   └── editing_tasks.py
│   │   └── main.py
│   ├── storage/
│   │   ├── uploads/
│   │   ├── projects/
│   │   └── outputs/
│   └── requirements.txt
└── frontend/
    ├── public/
    ├── src/
    │   ├── components/
    │   ├── views/
    │   ├── services/
    │   └── App.vue
    └── package.json