#!/usr/bin/env python3
"""Test API with curl"""

import subprocess
import json
import os

def test_curl_api():
    """Test API using curl"""
    print("🔍 Testing API with curl...")
    
    # Create test video file
    test_video_path = os.path.join(os.getcwd(), "storage", "test_video.mp4")
    os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
    
    # Create a dummy video file
    with open(test_video_path, 'wb') as f:
        f.write(b'\x00' * 1024)  # 1KB dummy file
    
    print(f"📋 Created test file: {test_video_path}")
    
    # Prepare data
    data = {
        "project_id": "b9da6931-653e-4a2c-a575-a26dadeb6c08",
        "main_video_path": test_video_path,
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0
    }
    
    # Convert to JSON
    json_data = json.dumps(data)
    
    # Curl command
    curl_cmd = [
        "curl",
        "-X", "POST",
        "http://localhost:8000/api/editing/start",
        "-H", "Content-Type: application/json",
        "-d", json_data,
        "-v"
    ]
    
    print(f"📋 Curl command: {' '.join(curl_cmd)}")
    
    try:
        # Execute curl
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=30)
        
        print(f"📋 Return code: {result.returncode}")
        print(f"📋 STDOUT: {result.stdout}")
        print(f"📋 STDERR: {result.stderr}")
        
    except subprocess.TimeoutExpired:
        print("❌ Curl request timed out")
    except Exception as e:
        print(f"❌ Curl error: {e}")
    
    # Clean up
    if os.path.exists(test_video_path):
        os.remove(test_video_path)
        print(f"🧹 Cleaned up test file: {test_video_path}")

if __name__ == "__main__":
    test_curl_api()
