#!/usr/bin/env python3
"""Test task endpoint"""

import requests

def test_task_endpoint():
    """Test task endpoint"""
    print("🔍 Testing task endpoint...")
    
    try:
        response = requests.post('http://localhost:8000/api/editing/test-task')
        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response text: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Task test successful!")
            print(f"📋 Result: {result}")
        else:
            print(f"❌ Task test failed with status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Task test error: {e}")

if __name__ == "__main__":
    test_task_endpoint()
