#!/usr/bin/env python3
"""Test schedule method"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_schedule_method():
    """Test schedule method directly"""
    print("🔍 Testing schedule method...")
    
    try:
        # Import task
        from app.tasks.editing_tasks import process_video_task
        
        # Test data
        job_id = "test-job-123"
        main_video_path = "nonexistent.mp4"
        config = {
            'project_id': 'b9da6931-653e-4a2c-a575-a26dadeb6c08',
            'subtitle_effect': 'none',
            'background_volume': 0.3,
            'audio_volume': 1.0
        }
        
        print(f"📋 Task type: {type(process_video_task)}")
        print(f"📋 Task attributes: {[attr for attr in dir(process_video_task) if not attr.startswith('_')]}")
        
        # Test schedule with delay=0
        print("📋 Testing schedule with delay=0...")
        try:
            result = process_video_task.schedule(args=(job_id, main_video_path, config), delay=0)
            print(f"✅ Schedule with delay=0 result: {result}")
        except Exception as e:
            print(f"❌ Schedule with delay=0 failed: {e}")
            print(f"📋 Error type: {type(e)}")
            import traceback
            traceback.print_exc()
            
        # Test schedule with eta
        print("📋 Testing schedule with eta...")
        try:
            import datetime
            eta = datetime.datetime.now() + datetime.timedelta(seconds=1)
            result = process_video_task.schedule(args=(job_id, main_video_path, config), eta=eta)
            print(f"✅ Schedule with eta result: {result}")
        except Exception as e:
            print(f"❌ Schedule with eta failed: {e}")
            print(f"📋 Error type: {type(e)}")
            
    except Exception as e:
        print(f"❌ Import or setup error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_schedule_method()
