# 應用配置
APP_NAME=Auto Video Editor
APP_VERSION=1.0.0
DEBUG=false

# 數據庫配置
DATABASE_URL=sqlite:///./storage/app.db

# 文件存儲配置
UPLOAD_DIR=storage/uploads
PROJECT_DIR=storage/projects
OUTPUT_DIR=storage/outputs
MAX_FILE_SIZE=524288000

# FFmpeg 配置（可選，如果不設置將使用系統PATH中的ffmpeg）
# FFMPEG_PATH=/usr/bin/ffmpeg
# FFPROBE_PATH=/usr/bin/ffprobe

# 視頻處理配置
DEFAULT_VIDEO_CODEC=libx264
DEFAULT_AUDIO_CODEC=aac
DEFAULT_VIDEO_BITRATE=2M
DEFAULT_AUDIO_BITRATE=128k

# 字幕配置
DEFAULT_SUBTITLE_FONT=Arial
DEFAULT_SUBTITLE_SIZE=24
DEFAULT_SUBTITLE_COLOR=white

# 任務隊列配置
HUEY_DB_PATH=storage/huey.db
MAX_CONCURRENT_TASKS=2

# API 配置
API_PREFIX=/api
CORS_ORIGINS=["*"]

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
