@echo off
chcp 65001 >nul
echo ========================================
echo 🎬 自動視頻剪輯平台 - Windows 環境設置
echo ========================================
echo.

:: 檢查 Python 是否安裝
echo 🔍 檢查 Python 環境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安裝或未添加到 PATH
    echo 請先安裝 Python 3.8+ 並添加到系統 PATH
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 檢查 Node.js 是否安裝
echo 🔍 檢查 Node.js 環境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安裝或未添加到 PATH
    echo 請先安裝 Node.js 16+ 並添加到系統 PATH
    echo 下載地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 檢查 npm 是否可用
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安裝或未添加到 PATH
    pause
    exit /b 1
)

echo ✅ Python 和 Node.js 環境檢查通過
echo.

:: 創建虛擬環境
echo 🔧 創建 Python 虛擬環境...
if exist "venv" (
    echo ⚠️  虛擬環境已存在，是否重新創建？ (y/n)
    set /p recreate=
    if /i "%recreate%"=="y" (
        echo 🗑️  刪除現有虛擬環境...
        rmdir /s /q venv
    ) else (
        echo 📦 使用現有虛擬環境...
        goto :activate_venv
    )
)

python -m venv venv
if errorlevel 1 (
    echo ❌ 虛擬環境創建失敗
    pause
    exit /b 1
)
echo ✅ 虛擬環境創建成功

:activate_venv
:: 激活虛擬環境
echo 🚀 激活虛擬環境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虛擬環境激活失敗
    pause
    exit /b 1
)
echo ✅ 虛擬環境已激活

:: 升級 pip
echo 📦 升級 pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️  pip 升級失敗，繼續安裝...
)

:: 安裝後端依賴
echo 📦 安裝後端依賴...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 後端依賴安裝失敗
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ 後端依賴安裝完成

:: 創建環境配置文件
echo 🔧 創建環境配置文件...
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo ✅ 已創建 backend\.env 配置文件
) else (
    echo ⚠️  backend\.env 已存在，跳過創建
)

:: 創建存儲目錄
echo 📁 創建存儲目錄...
if not exist "storage" mkdir storage
if not exist "storage\uploads" mkdir storage\uploads
if not exist "storage\projects" mkdir storage\projects
if not exist "storage\outputs" mkdir storage\outputs
if not exist "storage\temp" mkdir storage\temp
echo ✅ 存儲目錄創建完成

:: 初始化數據庫
echo 🗄️  初始化數據庫...
cd backend
python -c "from app.core.database import init_db; init_db(); print('數據庫初始化完成')"
if errorlevel 1 (
    echo ❌ 數據庫初始化失敗
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ 數據庫初始化完成

:: 安裝前端依賴
echo 📦 安裝前端依賴...
cd frontend
npm install
if errorlevel 1 (
    echo ❌ 前端依賴安裝失敗
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ 前端依賴安裝完成

:: 檢查 FFmpeg（可選）
echo 🔍 檢查 FFmpeg...
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  FFmpeg 未安裝或未添加到 PATH
    echo 建議安裝 FFmpeg 以獲得更好的視頻處理性能
    echo 下載地址: https://ffmpeg.org/download.html
    echo 或使用 chocolatey: choco install ffmpeg
) else (
    echo ✅ FFmpeg 已安裝
)

echo.
echo 🎉 環境設置完成！
echo.
echo 📋 下一步操作：
echo 1. 運行 start_windows.bat 啟動系統
echo 2. 或手動啟動：
echo    - 後端: run_backend.bat
echo    - 前端: run_frontend.bat
echo    - 測試: test_windows.bat
echo.
echo 📍 服務地址：
echo - 前端: http://localhost:3000
echo - 後端: http://localhost:8000
echo - API 文檔: http://localhost:8000/docs
echo.
pause
