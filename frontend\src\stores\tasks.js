import { defineStore } from 'pinia'
import { progressAPI, editingAPI } from '@/services/api'

export const useTasksStore = defineStore('tasks', {
  state: () => ({
    tasks: [],
    currentTask: null,
    stats: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    },
    loading: false,
    error: null,
    pollingInterval: null
  }),

  getters: {
    getTaskById: (state) => (id) => {
      return state.tasks.find(task => task.id === id)
    },

    activeTasks: (state) => {
      return state.tasks.filter(task => 
        task.status === 'pending' || task.status === 'processing'
      )
    },

    completedTasks: (state) => {
      return state.tasks.filter(task => task.status === 'completed')
    },

    failedTasks: (state) => {
      return state.tasks.filter(task => task.status === 'failed')
    },

    hasActiveTasks: (state) => {
      return state.tasks.some(task => 
        task.status === 'pending' || task.status === 'processing'
      )
    }
  },

  actions: {
    async fetchTasks(params = {}) {
      this.loading = true
      this.error = null

      try {
        const response = await progressAPI.getTasks(params)
        this.tasks = response.jobs || []
        return response
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchTaskProgress(taskId) {
      try {
        const task = await progressAPI.getTaskProgress(taskId)
        
        // 更新任務列表中的任務
        const index = this.tasks.findIndex(t => t.id === taskId)
        if (index !== -1) {
          this.tasks[index] = task
        }
        
        // 更新當前任務
        if (this.currentTask && this.currentTask.id === taskId) {
          this.currentTask = task
        }
        
        return task
      } catch (error) {
        this.error = error.message
        throw error
      }
    },

    async fetchStats() {
      try {
        const stats = await progressAPI.getStats()
        this.stats = stats
        return stats
      } catch (error) {
        this.error = error.message
        throw error
      }
    },

    async startEditing(editingData) {
      this.loading = true
      this.error = null

      try {
        const response = await editingAPI.startEditing(editingData)
        
        // 添加新任務到列表
        if (response.job_id) {
          const newTask = {
            id: response.job_id,
            status: 'pending',
            progress: 0,
            created_at: new Date().toISOString(),
            ...editingData
          }
          this.tasks.unshift(newTask)
        }
        
        return response
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async cancelTask(taskId) {
      try {
        await editingAPI.cancelTask(taskId)
        
        // 更新任務狀態
        const index = this.tasks.findIndex(t => t.id === taskId)
        if (index !== -1) {
          this.tasks[index].status = 'cancelled'
        }
        
        return true
      } catch (error) {
        this.error = error.message
        throw error
      }
    },

    async retryTask(taskId) {
      try {
        const response = await editingAPI.retryTask(taskId)
        
        // 更新任務狀態
        const index = this.tasks.findIndex(t => t.id === taskId)
        if (index !== -1) {
          this.tasks[index].status = 'pending'
          this.tasks[index].progress = 0
          this.tasks[index].error = null
        }
        
        return response
      } catch (error) {
        this.error = error.message
        throw error
      }
    },

    setCurrentTask(task) {
      this.currentTask = task
    },

    clearCurrentTask() {
      this.currentTask = null
    },

    // 開始輪詢活動任務
    startPolling() {
      if (this.pollingInterval) {
        return
      }

      this.pollingInterval = setInterval(async () => {
        if (this.hasActiveTasks) {
          try {
            await this.fetchTasks()
            await this.fetchStats()
          } catch (error) {
            console.error('Polling error:', error)
          }
        }
      }, 2000) // 每2秒輪詢一次
    },

    // 停止輪詢
    stopPolling() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }
    },

    clearError() {
      this.error = null
    }
  }
})
