#!/usr/bin/env python3
"""
測試編輯進度功能
"""
import requests
import json
import time

def test_editing_progress():
    base_url = 'http://localhost:8000'
    
    # 編輯配置
    config = {
        'project_id': 'e26a251a-0e00-4db7-a3fc-24b0341e2277',
        'main_video_filename': '1.MOV',  # 使用不同的文件
        'output_size': '720x720',
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print('🎬 開始新的編輯任務測試')
    print('配置:', json.dumps(config, indent=2, ensure_ascii=False))
    
    # 開始編輯
    response = requests.post(f'{base_url}/api/editing/start', json=config)
    print(f'狀態碼: {response.status_code}')
    
    if response.status_code != 200:
        print(f'❌ 編輯啟動失敗: {response.text}')
        return
    
    result = response.json()
    job_id = result.get('job_id')
    print(f'✅ 任務已啟動，ID: {job_id}')
    
    # 監控進度
    print('\n📊 監控任務進度:')
    for i in range(1, 11):
        response = requests.get(f'{base_url}/api/progress/{job_id}')
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            progress = data.get('progress', 0)
            print(f'第{i}次檢查 - 狀態: {status}, 進度: {progress}%')
            
            if status == 'completed':
                output_path = data.get('output_path', 'N/A')
                print(f'✅ 編輯完成! 輸出: {output_path}')
                break
            elif status == 'failed':
                error = data.get('error', 'Unknown error')
                print(f'❌ 編輯失敗: {error}')
                break
        else:
            print(f'⚠️ 無法獲取進度: {response.status_code}')
        
        if i < 10:
            time.sleep(2)
    
    print('\n🎯 測試完成')

if __name__ == '__main__':
    test_editing_progress()
