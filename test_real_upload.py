#!/usr/bin/env python3
"""
真實文件上傳和刪除測試
模擬前端的真實上傳流程
"""

import os
import sys
import requests
import json

# 添加後端路徑
sys.path.append('backend')

from backend.app.core.database import get_db
from backend.app.models.project import create_project, ProjectCreate

def test_real_upload_and_delete():
    print("=== 真實文件上傳和刪除測試 ===")
    
    # 1. 創建項目
    project_id = "real-test-project"
    project_data = ProjectCreate(
        name="Real Test Project",
        description="Test project for file deletion"
    )
    
    print(f"=== 創建項目 {project_id} ===")
    create_project(project_data, project_id)
    
    # 2. 創建測試文件
    project_upload_dir = f"storage/uploads/{project_id}"
    os.makedirs(project_upload_dir, exist_ok=True)
    
    test_file_path = os.path.join(project_upload_dir, "test_video.mp4")
    with open(test_file_path, "w") as f:
        f.write("This is a test video file content")
    
    print(f"✓ 創建測試文件: {test_file_path}")
    
    # 3. 模擬上傳API調用，添加文件記錄到數據庫
    files = [test_file_path]
    
    # 使用真實的上傳API
    url = "http://localhost:8000/api/uploads/"
    
    with open(test_file_path, "rb") as f:
        files_data = {
            'files': ('test_video.mp4', f, 'video/mp4')
        }
        form_data = {
            'project_id': project_id,
            'project_name': 'Real Test Project',
            'project_description': 'Test project for file deletion'
        }
        
        print("=== 調用上傳API ===")
        response = requests.post(url, files=files_data, data=form_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 上傳成功: {result}")
            file_id = result['files'][0]['id']
        else:
            print(f"✗ 上傳失敗: {response.status_code} - {response.text}")
            return
    
    # 4. 檢查數據庫記錄
    print("\n=== 檢查數據庫記錄 ===")
    db = get_db()
    files = db.execute_query("SELECT * FROM project_files WHERE project_id = ?", (project_id,))
    
    if files:
        file_record = files[0]
        file_id = file_record['id']
        print(f"✓ 找到文件記錄: ID={file_id}, 文件名={file_record['filename']}")
        print(f"  路徑: {file_record['file_path']}")
        print(f"  類型: {file_record['file_type']}")
    else:
        print("✗ 沒有找到文件記錄")
        return
    
    # 5. 測試刪除API
    print(f"\n=== 測試刪除API (文件 ID: {file_id}) ===")
    delete_url = f"http://localhost:8000/api/files/{file_id}"
    
    delete_response = requests.delete(delete_url)
    
    print(f"響應狀態碼: {delete_response.status_code}")
    print(f"響應內容: {delete_response.text}")
    
    if delete_response.status_code == 200:
        print("✓ 刪除API調用成功")
    else:
        print("✗ 刪除API調用失敗")
        return
    
    # 6. 驗證刪除結果
    print("\n=== 驗證刪除結果 ===")
    
    # 檢查數據庫記錄
    files_after = db.execute_query("SELECT * FROM project_files WHERE id = ?", (file_id,))
    if files_after:
        print("✗ 數據庫記錄仍然存在")
    else:
        print("✓ 數據庫記錄已刪除")
    
    # 檢查物理文件
    if os.path.exists(test_file_path):
        print("✗ 物理文件仍然存在")
    else:
        print("✓ 物理文件已刪除")
    
    print("\n=== 測試完成 ===")

if __name__ == "__main__":
    test_real_upload_and_delete()
