#!/usr/bin/env python3
"""
檢查數據庫中文件的file_type字段
"""

import sys
import os
import sqlite3

# 直接使用SQLite連接
def get_db_connection():
    # 檢查可能的數據庫位置
    possible_paths = [
        'backend/storage/app.db',
        'storage/app.db',
        'backend/app.db',
        'app.db'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到數據庫: {path}")
            return sqlite3.connect(path)

    raise FileNotFoundError("找不到數據庫文件")

def check_file_types():
    """檢查數據庫中的文件類型"""
    print("🔍 檢查數據庫中的文件類型...")

    try:
        conn = get_db_connection()
        conn.row_factory = sqlite3.Row  # 讓結果可以像字典一樣訪問
        
        # 獲取項目信息
        projects_query = "SELECT id, name FROM projects WHERE status = 'active' ORDER BY created_at DESC LIMIT 5"
        cursor = conn.cursor()
        cursor.execute(projects_query)
        projects = cursor.fetchall()
        
        print(f"找到 {len(projects)} 個活躍項目:")
        
        for project in projects:
            project_id = project['id']
            project_name = project['name']
            print(f"\n📁 項目: {project_name} (ID: {project_id})")
            
            # 獲取項目文件
            files_query = "SELECT id, filename, file_type, file_size, file_path FROM project_files WHERE project_id = ? ORDER BY created_at DESC"
            cursor.execute(files_query, (project_id,))
            files = cursor.fetchall()
            
            if files:
                print(f"   文件數量: {len(files)}")
                
                # 統計文件類型
                type_counts = {}
                for file in files:
                    file_type = file['file_type'] or 'unknown'
                    type_counts[file_type] = type_counts.get(file_type, 0) + 1
                
                print(f"   文件類型統計: {type_counts}")
                
                # 顯示前5個文件的詳細信息
                print("   文件詳情:")
                for i, file in enumerate(files[:5]):
                    file_id = file['id']
                    filename = file['filename']
                    file_type = file['file_type'] or 'NULL'
                    file_size = file['file_size'] or 0
                    file_path = file['file_path']
                    
                    # 檢查文件是否存在
                    exists = "✅" if os.path.exists(file_path) else "❌"
                    
                    print(f"     {i+1}. {filename}")
                    print(f"        ID: {file_id}, 類型: {file_type}, 大小: {file_size:,} bytes")
                    print(f"        路徑: {file_path} {exists}")
                
                if len(files) > 5:
                    print(f"     ... 還有 {len(files) - 5} 個文件")
                    
                # 特別檢查視頻文件
                video_files = [f for f in files if f['file_type'] == 'video']
                if video_files:
                    print(f"   🎬 視頻文件: {len(video_files)} 個")
                    for vf in video_files[:3]:
                        print(f"     - {vf['filename']} (ID: {vf['id']})")
                else:
                    print("   ⚠️ 沒有找到視頻文件!")
                    
                    # 檢查是否有.MOV文件但類型不是video
                    mov_files = [f for f in files if f['filename'].lower().endswith('.mov')]
                    if mov_files:
                        print(f"   🔍 發現.MOV文件但類型不是video:")
                        for mf in mov_files:
                            print(f"     - {mf['filename']}: 類型={mf['file_type']}")
            else:
                print("   沒有文件")
    
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_file_types()
