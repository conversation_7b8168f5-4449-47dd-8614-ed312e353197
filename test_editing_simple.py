#!/usr/bin/env python3
"""Simple test for editing API"""

import requests
import json

def test_editing_simple():
    """Test editing API with minimal data"""
    print("🔍 Testing editing API with minimal data...")
    
    # Test with minimal required fields
    editing_data = {
        'project_id': 'b9da6931-653e-4a2c-a575-a26dadeb6c08',
        'main_video_path': '/nonexistent/path.mp4'
    }
    
    print(f"📋 Testing with data: {editing_data}")
    
    try:
        response = requests.post('http://localhost:8000/api/editing/start', json=editing_data)
        print(f"Editing API status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Editing started successfully!")
            print(f"📋 Job ID: {result.get('job_id')}")
        elif response.status_code == 500:
            print("❌ 500 Internal Server Error detected!")
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"📋 Error detail: {error_data.get('detail', 'No detail')}")
            except:
                print("📋 Could not parse error response")
        else:
            print(f"📋 Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")

if __name__ == "__main__":
    test_editing_simple()
