#!/usr/bin/env python3
"""測試文件刪除功能"""

import sys
import os
import uuid
import requests
import json
sys.path.append('backend')

from backend.app.core.database import get_db
from backend.app.models.project import create_project

def create_test_data():
    """創建測試數據"""
    print("\n=== 創建測試數據 ===")

    # 創建測試項目
    project_data = {
        "name": "Test Project",
        "description": "Project for testing file deletion"
    }

    try:
        response = requests.post("http://localhost:8000/api/projects", json=project_data)
        if response.status_code == 200:
            project = response.json()
            print(f"✓ 創建項目成功: {project['name']} (ID: {project['id']})")
            return project['id']
        else:
            print(f"✗ 創建項目失敗: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ 創建項目異常: {e}")
        return None

def upload_test_file(project_id):
    """上傳測試文件"""
    print("\n=== 上傳測試文件 ===")

    # 創建測試文件
    test_content = "This is test file content"
    test_filename = "test_file.txt"

    try:
        files = {'file': (test_filename, test_content, 'text/plain')}
        data = {'project_id': project_id}

        response = requests.post("http://localhost:8000/api/uploads/", files=files, data=data)
        if response.status_code == 200:
            file_info = response.json()
            print(f"✓ 上傳文件成功: {file_info['filename']} (ID: {file_info['id']})")
            return file_info['id']
        else:
            print(f"✗ 上傳文件失敗: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ 上傳文件異常: {e}")
        return None

def test_file_delete(file_id):
    """測試文件刪除"""
    print(f"\n=== 測試文件刪除 (ID: {file_id}) ===")

    try:
        response = requests.delete(f"http://localhost:8000/api/files/{file_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 刪除文件成功: {result['message']}")
            return True
        else:
            print(f"✗ 刪除文件失敗: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 刪除文件異常: {e}")
        return False

def check_database():
    """檢查數據庫狀態"""
    print("\n=== 檢查數據庫狀態 ===")

    db = get_db()

    print("\n1. 檢查項目:")
    projects = db.execute_query("SELECT id, name FROM projects LIMIT 5")
    if projects:
        print(f"  找到 {len(projects)} 個項目:")
        for project in projects:
            print(f"    ID: {project['id']}, 名稱: {project['name']}")
    else:
        print("  沒有找到項目")

    print("\n2. 檢查項目文件:")
    files = db.execute_query("SELECT id, filename, project_id FROM project_files LIMIT 10")
    if files:
        print(f"  找到 {len(files)} 個文件:")
        for file in files:
            print(f"    ID: {file['id']}, 文件名: {file['filename']}, 項目ID: {file['project_id']}")
    else:
        print("  沒有找到文件")

def main():
    print("=== 測試文件刪除功能 ===")

    # 檢查初始狀態
    check_database()

    # 創建測試數據
    project_id = create_test_data()
    if not project_id:
        print("無法創建測試項目，退出測試")
        return

    # 上傳測試文件
    file_id = upload_test_file(project_id)
    if not file_id:
        print("無法上傳測試文件，退出測試")
        return

    # 檢查上傳後狀態
    print("\n=== 上傳後狀態 ===")
    check_database()

    # 測試文件刪除
    success = test_file_delete(file_id)

    # 檢查刪除後狀態
    print("\n=== 刪除後狀態 ===")
    check_database()

    if success:
        print("\n🎉 文件刪除功能測試成功！")
    else:
        print("\n❌ 文件刪除功能測試失敗！")

if __name__ == "__main__":
    main()
