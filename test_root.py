#!/usr/bin/env python3
"""Test root endpoint"""

import requests

def test_root():
    """Test root endpoint"""
    print("🔍 Testing root endpoint...")
    
    try:
        response = requests.get('http://localhost:8000/')
        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response text: {response.text}")
        
        if response.status_code == 200:
            print("✅ Root endpoint working!")
        else:
            print(f"❌ Root endpoint failed with status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")

if __name__ == "__main__":
    test_root()
