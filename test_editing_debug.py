#!/usr/bin/env python3
"""Debug test for editing API"""

import requests
import json
import traceback

def test_editing_debug():
    """Test editing API with debug info"""
    print("🔍 Testing editing API with debug info...")
    
    # Create a dummy video file for testing
    import os
    test_video_path = os.path.abspath("storage/test_video.mp4")
    os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
    
    # Create a dummy file
    with open(test_video_path, "w") as f:
        f.write("dummy video content")
    
    print(f"📋 Created test file: {test_video_path}")
    
    # Test editing endpoint
    editing_data = {
        'project_id': 'b9da6931-653e-4a2c-a575-a26dadeb6c08',
        'main_video_path': test_video_path,
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print(f"📋 Testing with data: {editing_data}")
    
    try:
        # Make request with detailed error handling
        response = requests.post(
            'http://localhost:8000/api/editing/start', 
            json=editing_data,
            timeout=30
        )
        
        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        print(f"📋 Response text: {response.text}")
        
        if response.status_code == 500:
            print("❌ 500 Internal Server Error detected!")
            try:
                error_data = response.json()
                print(f"📋 Error detail: {error_data.get('detail', 'No detail')}")
                
                # Check if error contains specific information
                detail = error_data.get('detail', '')
                if 'delay' in detail:
                    print("🔍 Error mentions 'delay' - this is the issue we're tracking")
                if 'TaskWrapper' in detail:
                    print("🔍 Error mentions 'TaskWrapper' - Huey task issue")
                    
            except Exception as parse_error:
                print(f"📋 Could not parse error response: {parse_error}")
        elif response.status_code == 200:
            print("✅ Request successful!")
            result = response.json()
            print(f"📋 Job ID: {result.get('job_id')}")
        else:
            print(f"📋 Unexpected status: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        print(f"📋 Full traceback:")
        traceback.print_exc()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"📋 Full traceback:")
        traceback.print_exc()
    finally:
        # Clean up
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
            print(f"🧹 Cleaned up test file: {test_video_path}")

if __name__ == "__main__":
    test_editing_debug()
