#!/usr/bin/env python3
"""Debug database issues"""

import sqlite3
import os

def check_database():
    """Check database content"""
    db_path = "storage/app.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return
    
    print(f"🔍 Checking database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check projects table
        print("\n📋 Projects table:")
        cursor.execute("SELECT * FROM projects ORDER BY created_at DESC LIMIT 10")
        projects = cursor.fetchall()
        
        if projects:
            print(f"Found {len(projects)} projects:")
            for project in projects:
                print(f"  - ID: {project[0]}")
                print(f"    Name: {project[1]}")
                print(f"    Created: {project[2]}")
                print(f"    Status: {project[4]}")
                print()
        else:
            print("No projects found")
        
        # Check project_files table
        print("\n📋 Project files table:")
        cursor.execute("SELECT * FROM project_files ORDER BY created_at DESC LIMIT 10")
        files = cursor.fetchall()
        
        if files:
            print(f"Found {len(files)} files:")
            for file in files:
                print(f"  - File ID: {file[0]}")
                print(f"    Project ID: {file[1]}")
                print(f"    Filename: {file[2]}")
                print(f"    Type: {file[4]}")
                print()
        else:
            print("No files found")
        
        # Check table schema
        print("\n📋 Projects table schema:")
        cursor.execute("PRAGMA table_info(projects)")
        schema = cursor.fetchall()
        for column in schema:
            print(f"  - {column[1]} ({column[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

def test_project_creation():
    """Test project creation directly"""
    print("\n🔍 Testing direct project creation...")
    
    try:
        from backend.app.models.project import create_project, get_project, ProjectCreate
        
        # Create test project
        project_data = ProjectCreate(
            name="Direct Test Project",
            description="Testing direct project creation"
        )
        
        project_id = create_project(project_data)
        print(f"✅ Created project with ID: {project_id}")
        
        # Try to retrieve it
        retrieved_project = get_project(project_id)
        if retrieved_project:
            print(f"✅ Retrieved project: {retrieved_project['name']}")
        else:
            print(f"❌ Failed to retrieve project {project_id}")
            
    except Exception as e:
        print(f"❌ Project creation error: {e}")

if __name__ == "__main__":
    print("🚀 Debugging database...")
    check_database()
    test_project_creation()
    print("\n✅ Database debugging complete!")
