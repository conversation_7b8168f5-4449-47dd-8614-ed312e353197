#!/usr/bin/env python3
"""
測試前端完整流程 - 模擬用戶在前端的操作
"""

import requests
import json
import time

def test_complete_frontend_flow():
    """測試完整的前端流程"""
    base_url = 'http://localhost:8000'
    project_id = 'e26a251a-0e00-4db7-a3fc-24b0341e2277'
    
    print("🌐 測試完整前端流程...")
    print("=" * 60)
    
    # 步驟1: 模擬前端加載項目詳情頁面
    print("\n1️⃣ 加載項目詳情頁面...")
    response = requests.get(f'{base_url}/api/projects/{project_id}')
    if response.status_code == 200:
        project = response.json()
        print(f"✅ 項目加載成功: {project['name']}")
    else:
        print(f"❌ 項目加載失敗: {response.status_code}")
        return
    
    # 步驟2: 加載項目文件列表
    print("\n2️⃣ 加載項目文件列表...")
    response = requests.get(f'{base_url}/api/projects/{project_id}/files')
    if response.status_code == 200:
        files = response.json()
        print(f"✅ 文件列表加載成功: {len(files)} 個文件")
        
        # 分析文件類型
        video_files = [f for f in files if f['file_type'] == 'video']
        audio_files = [f for f in files if f['file_type'] == 'audio']
        subtitle_files = [f for f in files if f['file_type'] == 'subtitle']
        
        print(f"   📹 視頻文件: {len(video_files)} 個")
        print(f"   🎵 音頻文件: {len(audio_files)} 個")
        print(f"   📝 字幕文件: {len(subtitle_files)} 個")
        
        if video_files:
            main_video = video_files[0]
            print(f"   主視頻: {main_video['filename']}")
        else:
            print("❌ 沒有視頻文件，無法進行編輯")
            return
    else:
        print(f"❌ 文件列表加載失敗: {response.status_code}")
        return
    
    # 步驟3: 檢查現有任務
    print("\n3️⃣ 檢查現有任務...")
    response = requests.get(f'{base_url}/api/progress/?project_id={project_id}')
    if response.status_code == 200:
        tasks = response.json()
        print(f"✅ 任務列表加載成功: {len(tasks)} 個任務")

        # 檢查是否有進行中的任務
        if isinstance(tasks, list) and len(tasks) > 0:
            active_tasks = [t for t in tasks if isinstance(t, dict) and t.get('status') in ['pending', 'processing']]
            if active_tasks:
                print(f"⚠️ 有 {len(active_tasks)} 個進行中的任務")
                for task in active_tasks:
                    print(f"   - {task['job_id']}: {task['status']} ({task['progress']}%)")
            else:
                print("✅ 沒有進行中的任務，可以開始新編輯")
        else:
            print("✅ 沒有任務記錄，可以開始新編輯")
    else:
        print(f"❌ 任務列表加載失敗: {response.status_code}")
    
    # 步驟4: 模擬前端編輯配置
    print("\n4️⃣ 準備編輯配置...")
    
    # 這是前端默認的編輯配置
    editing_config = {
        'project_id': project_id,
        'main_video_filename': main_video['filename'],
        'output_size': '1080x1920',  # 豎屏格式
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0,
        'intro_text': '',
        'outro_text': '',
        'background_music': ''
    }
    
    # 添加音頻和字幕文件（如果有）
    if audio_files:
        editing_config['background_music_filename'] = audio_files[0]['filename']
        print(f"   🎵 背景音樂: {audio_files[0]['filename']}")
    
    if subtitle_files:
        editing_config['subtitle_filename'] = subtitle_files[0]['filename']
        print(f"   📝 字幕文件: {subtitle_files[0]['filename']}")
    
    print("📝 編輯配置:")
    print(json.dumps(editing_config, indent=2, ensure_ascii=False))
    
    # 步驟5: 模擬用戶確認對話框
    print("\n5️⃣ 模擬用戶確認...")
    confirm_message = f"""確定要開始編輯任務嗎？

主視頻：{main_video['filename']}
音頻文件：{audio_files[0]['filename'] if audio_files else '無'}
字幕文件：{subtitle_files[0]['filename'] if subtitle_files else '無'}

編輯配置將使用當前的設置。"""
    
    print(confirm_message)
    print("✅ 用戶確認開始編輯")
    
    # 步驟6: 發送編輯請求
    print("\n6️⃣ 發送編輯請求...")
    response = requests.post(f'{base_url}/api/editing/start', json=editing_config)
    
    if response.status_code == 200:
        result = response.json()
        job_id = result.get('job_id')
        print(f"✅ 編輯任務已啟動，任務ID: {job_id}")
        
        # 步驟7: 模擬前端監控進度
        print("\n7️⃣ 監控編輯進度...")
        monitor_editing_progress(base_url, job_id, project_id)
        
    else:
        print(f"❌ 編輯啟動失敗:")
        print(f"   狀態碼: {response.status_code}")
        print(f"   錯誤信息: {response.text}")
        
        # 嘗試解析錯誤詳情
        try:
            error_data = response.json()
            print(f"   詳細錯誤: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
        except:
            pass

def monitor_editing_progress(base_url, job_id, project_id):
    """監控編輯進度"""
    print(f"📊 開始監控任務: {job_id}")
    
    for i in range(1, 12):  # 最多監控12次（約1分鐘）
        try:
            # 獲取單個任務進度
            task_response = requests.get(f'{base_url}/api/progress/{job_id}')
            
            if task_response.status_code == 200:
                data = task_response.json()
                status = data.get('status', 'unknown')
                progress = data.get('progress', 0)
                
                print(f"第{i}次檢查 - 狀態: {status}, 進度: {progress}%")
                
                if status == 'completed':
                    output_path = data.get('output_path', 'N/A')
                    print(f"🎉 編輯完成! 輸出文件: {output_path}")
                    
                    # 檢查輸出文件
                    check_output_file(output_path)
                    break
                    
                elif status == 'failed':
                    error = data.get('error', 'Unknown error')
                    print(f"💥 編輯失敗: {error}")
                    
                    # 獲取詳細錯誤信息
                    if 'details' in data:
                        print(f"詳細錯誤: {data['details']}")
                    break
                    
                elif status == 'cancelled':
                    print(f"⏹️ 編輯已取消")
                    break
            else:
                print(f"⚠️ 無法獲取進度: {task_response.status_code}")
        
        except Exception as e:
            print(f"❌ 檢查進度時出錯: {e}")
        
        if i < 11:
            print("   等待5秒...")
            time.sleep(5)
    
    print("📊 進度監控結束")

def check_output_file(output_path):
    """檢查輸出文件"""
    import os
    
    if not output_path or output_path == 'N/A':
        print("⚠️ 沒有輸出文件路徑")
        return
    
    # 嘗試不同的路徑組合
    possible_paths = [
        output_path,
        os.path.join('backend', output_path),
        output_path.replace('\\', '/'),
        os.path.join('backend', output_path.replace('\\', '/'))
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            print(f"📁 輸出文件確認存在: {path}")
            print(f"   文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            return
    
    print(f"❌ 輸出文件不存在: {output_path}")
    print("   嘗試的路徑:")
    for path in possible_paths:
        print(f"   - {path}")

if __name__ == "__main__":
    test_complete_frontend_flow()
