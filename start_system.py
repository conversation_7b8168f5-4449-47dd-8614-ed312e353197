#!/usr/bin/env python3
"""
自動視頻剪輯平台啟動腳本
自動啟動後端和前端服務
"""

import subprocess
import sys
import time
import os
import signal
from pathlib import Path

class SystemManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.huey_process = None
        
    def check_dependencies(self):
        """檢查依賴"""
        print("🔍 檢查系統依賴...")
        
        # 檢查 Python
        try:
            python_version = sys.version_info
            if python_version.major < 3 or python_version.minor < 8:
                print("❌ 需要 Python 3.8 或更高版本")
                return False
            print(f"✅ Python {python_version.major}.{python_version.minor}")
        except Exception as e:
            print(f"❌ Python 檢查失敗: {e}")
            return False
        
        # 檢查 Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js {result.stdout.strip()}")
            else:
                print("❌ Node.js 未安裝")
                return False
        except Exception as e:
            print("❌ Node.js 未安裝")
            return False
        
        # 檢查 npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm {result.stdout.strip()}")
            else:
                print("❌ npm 未安裝")
                return False
        except Exception as e:
            print("❌ npm 未安裝")
            return False
        
        return True
    
    def setup_backend(self):
        """設置後端環境"""
        print("\n🔧 設置後端環境...")
        
        # 檢查 requirements.txt
        if not Path("backend/requirements.txt").exists():
            print("❌ backend/requirements.txt 不存在")
            return False
        
        # 安裝 Python 依賴
        try:
            print("📦 安裝 Python 依賴...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
            ], check=True, capture_output=True, text=True)
            print("✅ Python 依賴安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ Python 依賴安裝失敗: {e}")
            print(e.stdout)
            print(e.stderr)
            return False
        
        # 創建存儲目錄
        try:
            directories = [
                "storage",
                "storage/uploads",
                "storage/projects",
                "storage/outputs",
                "storage/temp"
            ]
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
            print("✅ 存儲目錄創建完成")
        except Exception as e:
            print(f"❌ 存儲目錄創建失敗: {e}")
            return False
        
        # 初始化數據庫
        try:
            print("🗄️ 初始化數據庫...")
            os.chdir("backend")
            result = subprocess.run([
                sys.executable, "-c", 
                "from app.core.database import init_db; init_db()"
            ], check=True, capture_output=True, text=True)
            os.chdir("..")
            print("✅ 數據庫初始化完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 數據庫初始化失敗: {e}")
            print(e.stdout)
            print(e.stderr)
            os.chdir("..")
            return False
        
        return True
    
    def setup_frontend(self):
        """設置前端環境"""
        print("\n🔧 設置前端環境...")
        
        # 檢查 package.json
        if not Path("frontend/package.json").exists():
            print("❌ frontend/package.json 不存在")
            return False
        
        # 安裝 Node.js 依賴
        try:
            print("📦 安裝 Node.js 依賴...")
            os.chdir("frontend")
            result = subprocess.run(["npm", "install"], check=True, capture_output=True, text=True)
            os.chdir("..")
            print("✅ Node.js 依賴安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ Node.js 依賴安裝失敗: {e}")
            print(e.stdout)
            print(e.stderr)
            os.chdir("..")
            return False
        
        return True
    
    def start_backend(self):
        """啟動後端服務"""
        print("\n🚀 啟動後端服務...")
        try:
            os.chdir("backend")
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "app.main:app", 
                "--reload", "--host", "0.0.0.0", "--port", "8000"
            ])
            os.chdir("..")
            print("✅ 後端服務啟動成功 (http://localhost:8000)")
            return True
        except Exception as e:
            print(f"❌ 後端服務啟動失敗: {e}")
            os.chdir("..")
            return False
    
    def start_huey(self):
        """啟動任務隊列"""
        print("\n🚀 啟動任務隊列...")
        try:
            os.chdir("backend")
            self.huey_process = subprocess.Popen([
                sys.executable, "-m", "huey_consumer", "app.tasks.editing_tasks.huey"
            ])
            os.chdir("..")
            print("✅ 任務隊列啟動成功")
            return True
        except Exception as e:
            print(f"❌ 任務隊列啟動失敗: {e}")
            os.chdir("..")
            return False
    
    def start_frontend(self):
        """啟動前端服務"""
        print("\n🚀 啟動前端服務...")
        try:
            os.chdir("frontend")
            self.frontend_process = subprocess.Popen(["npm", "run", "dev"])
            os.chdir("..")
            print("✅ 前端服務啟動成功 (http://localhost:3000)")
            return True
        except Exception as e:
            print(f"❌ 前端服務啟動失敗: {e}")
            os.chdir("..")
            return False
    
    def wait_for_services(self):
        """等待服務啟動"""
        print("\n⏳ 等待服務啟動...")
        time.sleep(5)
        
        # 檢查後端服務
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ 後端服務運行正常")
            else:
                print("⚠️  後端服務可能未完全啟動")
        except Exception as e:
            print("⚠️  後端服務檢查失敗，可能仍在啟動中")
        
        # 檢查前端服務
        try:
            import requests
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print("✅ 前端服務運行正常")
            else:
                print("⚠️  前端服務可能未完全啟動")
        except Exception as e:
            print("⚠️  前端服務檢查失敗，可能仍在啟動中")
    
    def stop_services(self):
        """停止所有服務"""
        print("\n🛑 停止服務...")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ 後端服務已停止")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ 前端服務已停止")
        
        if self.huey_process:
            self.huey_process.terminate()
            print("✅ 任務隊列已停止")
    
    def run(self):
        """運行系統"""
        try:
            # 檢查依賴
            if not self.check_dependencies():
                print("❌ 依賴檢查失敗，請安裝必要的依賴")
                return
            
            # 設置環境
            if not self.setup_backend():
                print("❌ 後端環境設置失敗")
                return
            
            if not self.setup_frontend():
                print("❌ 前端環境設置失敗")
                return
            
            # 啟動服務
            if not self.start_backend():
                print("❌ 後端服務啟動失敗")
                return
            
            if not self.start_huey():
                print("❌ 任務隊列啟動失敗")
                return
            
            if not self.start_frontend():
                print("❌ 前端服務啟動失敗")
                return
            
            # 等待服務啟動
            self.wait_for_services()
            
            print("\n🎉 系統啟動完成！")
            print("\n📋 服務地址:")
            print("- 前端: http://localhost:3000")
            print("- 後端 API: http://localhost:8000")
            print("- API 文檔: http://localhost:8000/docs")
            print("\n按 Ctrl+C 停止服務")
            
            # 等待用戶中斷
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n\n收到停止信號...")
                self.stop_services()
                print("✅ 系統已停止")
        
        except Exception as e:
            print(f"❌ 系統運行錯誤: {e}")
            self.stop_services()

def main():
    """主函數"""
    print("🎬 自動視頻剪輯平台啟動器")
    print("=" * 50)
    
    manager = SystemManager()
    
    # 設置信號處理
    def signal_handler(sig, frame):
        print("\n\n收到停止信號...")
        manager.stop_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager.run()

if __name__ == "__main__":
    main()
