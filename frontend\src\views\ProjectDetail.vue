<template>
  <div class="project-detail">
    <div v-if="projectsStore.loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="project" class="project-content">
      <!-- 項目頭部 -->
      <div class="project-header">
        <div class="header-left">
          <el-button @click="$router.back()" circle>
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div class="project-info">
            <h1>{{ project.name }}</h1>
            <p>{{ project.description || '無描述' }}</p>
            <div class="project-meta">
              <el-tag :type="getStatusType(project.status)">
                {{ getStatusText(project.status) }}
              </el-tag>
              <span>創建於 {{ formatDate(project.created_at) }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="editProject">
            <el-icon><Edit /></el-icon>
            編輯項目
          </el-button>
          <el-button type="primary" @click="showUploadDialog">
            <el-icon><Upload /></el-icon>
            上傳文件
          </el-button>
        </div>
      </div>

      <!-- 項目統計 -->
      <div class="project-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon files">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ project.files?.length || 0 }}</div>
                  <div class="stat-label">文件數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon tasks">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ projectTasks.length }}</div>
                  <div class="stat-label">任務數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon processing">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ activeTasksCount }}</div>
                  <div class="stat-label">處理中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ completedTasksCount }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 標籤頁 -->
      <el-tabs v-model="activeTab" class="project-tabs">
        <!-- 文件列表 -->
        <el-tab-pane label="文件" name="files">
          <el-card>
            <template #header>
              <div class="tab-header">
                <span>項目文件</span>
                <el-button size="small" @click="showUploadDialog">
                  <el-icon><Plus /></el-icon>
                  添加文件
                </el-button>
              </div>
            </template>

            <div v-if="!project.files || project.files.length === 0" class="empty-state">
              <el-empty description="暫無文件" />
            </div>

            <el-table v-else :data="project.files" style="width: 100%">
              <el-table-column prop="filename" label="文件名" min-width="200" />
              <el-table-column prop="file_type" label="類型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getFileTypeColor(row.file_type)">
                    {{ getFileTypeText(row.file_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="file_size" label="大小" width="120">
                <template #default="{ row }">
                  {{ formatFileSize(row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="uploaded_at" label="上傳時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.uploaded_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="previewFile(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" @click="downloadFile(row)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteFile(row)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 任務列表 -->
        <el-tab-pane label="任務" name="tasks">
          <el-card>
            <template #header>
              <div class="tab-header">
                <span>編輯任務</span>
                <el-button size="small" type="primary" @click="startNewTask">
                  <el-icon><VideoPlay /></el-icon>
                  開始編輯
                </el-button>
              </div>
            </template>

            <div v-if="projectTasks.length === 0" class="empty-state">
              <el-empty description="暫無任務" />
            </div>

            <el-table v-else :data="projectTasks" style="width: 100%">
              <el-table-column prop="id" label="任務ID" width="150">
                <template #default="{ row }">
                  {{ row.id.substring(0, 8) }}...
                </template>
              </el-table-column>
              <el-table-column prop="status" label="狀態" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="進度" width="150">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.progress || 0"
                    :status="getProgressStatus(row.status)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="創建時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="viewTaskDetail(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'completed' && row.output_path"
                      size="small"
                      type="success"
                      @click="downloadResult(row)"
                    >
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'processing' || row.status === 'pending'"
                      size="small"
                      type="warning"
                      @click="cancelTask(row)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 編輯配置 -->
        <el-tab-pane label="編輯配置" name="config">
          <el-card>
            <el-form :model="editingConfig" label-width="120px">
              <el-form-item label="字幕效果">
                <el-select v-model="editingConfig.subtitle_effect" placeholder="選擇字幕效果">
                  <el-option label="無效果" value="none" />
                  <el-option label="卡拉OK" value="karaoke" />
                  <el-option label="打字機" value="typewriter" />
                  <el-option label="發光" value="glow" />
                  <el-option label="陰影" value="shadow" />
                </el-select>
              </el-form-item>
              <el-form-item label="背景音樂音量">
                <el-slider
                  v-model="editingConfig.background_volume"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  show-input
                />
              </el-form-item>
              <el-form-item label="音頻音量">
                <el-slider
                  v-model="editingConfig.audio_volume"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  show-input
                />
              </el-form-item>
              <el-form-item label="視頻尺寸">
                <el-select v-model="editingConfig.output_size" placeholder="選擇輸出尺寸">
                  <el-option label="保持原始" value="original" />
                  <el-option label="1920x1080" value="1920x1080" />
                  <el-option label="1280x720" value="1280x720" />
                  <el-option label="854x480" value="854x480" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveConfig">保存配置</el-button>
                <el-button @click="resetConfig">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-else class="error-state">
      <el-result
        icon="warning"
        title="項目不存在"
        sub-title="請檢查項目ID是否正確"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/projects')">
            返回項目列表
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 文件上傳對話框 -->
    <el-dialog v-model="uploadDialogVisible" title="上傳文件" width="500px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadAction"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :accept="acceptedTypes"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖到此處，或<em>點擊上傳</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 mp4, avi, mov, mkv, mp3, wav, aac, srt, ass, vtt 格式文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            上傳
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
  Plus, View, Download, Delete, VideoPlay, Close, UploadFilled
} from '@element-plus/icons-vue'

export default {
  name: 'ProjectDetail',
  components: {
    ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
    Plus, View, Download, Delete, VideoPlay, Close, UploadFilled
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectsStore = useProjectsStore()
    const tasksStore = useTasksStore()

    // 狀態
    const activeTab = ref('files')
    const uploadDialogVisible = ref(false)
    const uploading = ref(false)
    const uploadRef = ref(null)

    // 編輯配置
    const editingConfig = reactive({
      subtitle_effect: 'none',
      background_volume: 0.3,
      audio_volume: 1.0,
      output_size: 'original'
    })

    // 計算屬性
    const project = computed(() => projectsStore.currentProject)
    const projectTasks = computed(() => 
      tasksStore.tasks.filter(task => task.project_id === route.params.id)
    )
    const activeTasksCount = computed(() => 
      projectTasks.value.filter(task => 
        task.status === 'pending' || task.status === 'processing'
      ).length
    )
    const completedTasksCount = computed(() => 
      projectTasks.value.filter(task => task.status === 'completed').length
    )
    const uploadAction = computed(() => `/api/uploads/${route.params.id}`)
    const acceptedTypes = computed(() => '.mp4,.avi,.mov,.mkv,.mp3,.wav,.aac,.srt,.ass,.vtt')

    // 方法
    const loadProject = async () => {
      try {
        await projectsStore.fetchProject(route.params.id)
      } catch (error) {
        ElMessage.error('加載項目失敗')
      }
    }

    const loadTasks = async () => {
      try {
        await tasksStore.fetchTasks({ project_id: route.params.id })
      } catch (error) {
        console.error('Failed to load tasks:', error)
      }
    }

    const editProject = () => {
      // 這裡可以打開編輯項目的對話框
      ElMessage.info('編輯項目功能待實現')
    }

    const showUploadDialog = () => {
      uploadDialogVisible.value = true
    }

    const handleFileChange = (file, fileList) => {
      // 處理文件選擇
    }

    const beforeUpload = (file) => {
      const maxSize = 500 * 1024 * 1024 // 500MB
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超過 500MB')
        return false
      }
      return true
    }

    const submitUpload = async () => {
      // 實現文件上傳邏輯
      ElMessage.info('文件上傳功能待實現')
    }

    const previewFile = (file) => {
      ElMessage.info('文件預覽功能待實現')
    }

    const downloadFile = (file) => {
      ElMessage.info('文件下載功能待實現')
    }

    const deleteFile = (file) => {
      ElMessage.info('文件刪除功能待實現')
    }

    const startNewTask = () => {
      ElMessage.info('開始編輯功能待實現')
    }

    const viewTaskDetail = (task) => {
      router.push('/tasks')
    }

    const downloadResult = (task) => {
      ElMessage.info('下載結果功能待實現')
    }

    const cancelTask = async (task) => {
      try {
        await tasksStore.cancelTask(task.id)
        ElMessage.success('任務已取消')
        loadTasks()
      } catch (error) {
        ElMessage.error('取消任務失敗')
      }
    }

    const saveConfig = () => {
      ElMessage.success('配置已保存')
    }

    const resetConfig = () => {
      editingConfig.subtitle_effect = 'none'
      editingConfig.background_volume = 0.3
      editingConfig.audio_volume = 1.0
      editingConfig.output_size = 'original'
    }

    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-TW')
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '-'
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getStatusType = (status) => {
      const statusMap = {
        'active': 'success',
        'completed': 'info',
        'archived': 'warning',
        'pending': 'info',
        'processing': 'warning',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '進行中',
        'completed': '已完成',
        'archived': '已歸檔',
        'pending': '等待中',
        'processing': '處理中',
        'failed': '失敗'
      }
      return statusMap[status] || status
    }

    const getFileTypeColor = (type) => {
      const colorMap = {
        'video': 'primary',
        'audio': 'success',
        'subtitle': 'warning'
      }
      return colorMap[type] || 'info'
    }

    const getFileTypeText = (type) => {
      const typeMap = {
        'video': '視頻',
        'audio': '音頻',
        'subtitle': '字幕'
      }
      return typeMap[type] || type
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return undefined
    }

    // 監聽路由變化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadProject()
        loadTasks()
      }
    }, { immediate: true })

    onMounted(() => {
      loadProject()
      loadTasks()
    })

    return {
      projectsStore,
      project,
      projectTasks,
      activeTasksCount,
      completedTasksCount,
      activeTab,
      uploadDialogVisible,
      uploading,
      uploadRef,
      editingConfig,
      uploadAction,
      acceptedTypes,
      editProject,
      showUploadDialog,
      handleFileChange,
      beforeUpload,
      submitUpload,
      previewFile,
      downloadFile,
      deleteFile,
      startNewTask,
      viewTaskDetail,
      downloadResult,
      cancelTask,
      saveConfig,
      resetConfig,
      formatDate,
      formatFileSize,
      getStatusType,
      getStatusText,
      getFileTypeColor,
      getFileTypeText,
      getProgressStatus
    }
  }
}
</script>

<style scoped>
.project-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.project-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.project-info p {
  margin: 0 0 12px 0;
  color: #606266;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.project-stats {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.files {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.project-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.error-state {
  text-align: center;
  padding: 60px;
}
</style>
