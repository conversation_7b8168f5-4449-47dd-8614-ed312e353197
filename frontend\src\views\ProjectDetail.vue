<template>
  <div class="project-detail">
    <div v-if="projectsStore.loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="project" class="project-content">
      <!-- 項目頭部 -->
      <div class="project-header">
        <div class="header-left">
          <el-button @click="$router.back()" circle>
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div class="project-info">
            <h1>{{ project.name }}</h1>
            <p>{{ project.description || '無描述' }}</p>
            <div class="project-meta">
              <el-tag :type="getStatusType(project.status)">
                {{ getStatusText(project.status) }}
              </el-tag>
              <span>創建於 {{ formatDate(project.created_at) }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="editProject">
            <el-icon><Edit /></el-icon>
            編輯項目
          </el-button>
          <el-button type="primary" @click="showUploadDialog">
            <el-icon><Upload /></el-icon>
            上傳文件
          </el-button>
        </div>
      </div>

      <!-- 項目統計 -->
      <div class="project-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon files">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ project.files?.length || 0 }}</div>
                  <div class="stat-label">文件數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon tasks">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ projectTasks.length }}</div>
                  <div class="stat-label">任務數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon processing">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ activeTasksCount }}</div>
                  <div class="stat-label">處理中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ completedTasksCount }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 標籤頁 -->
      <el-tabs v-model="activeTab" class="project-tabs" tab-position="top">
        <!-- 文件列表 -->
        <el-tab-pane label="文件" name="files">
          <el-card class="tab-content-card">
            <template #header>
              <div class="tab-header">
                <span>項目文件</span>
                <el-button size="small" @click="showUploadDialog">
                  <el-icon><Plus /></el-icon>
                  添加文件
                </el-button>
              </div>
            </template>

            <div v-if="!project.files || project.files.length === 0" class="empty-state">
              <el-empty description="暫無文件" />
            </div>

            <el-table v-else :data="project.files" style="width: 100%">
              <el-table-column prop="filename" label="文件名" min-width="200" />
              <el-table-column prop="file_type" label="類型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getFileTypeColor(row.file_type)">
                    {{ getFileTypeText(row.file_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="file_size" label="大小" width="120">
                <template #default="{ row }">
                  {{ formatFileSize(row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="uploaded_at" label="上傳時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.uploaded_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="previewFile(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" @click="downloadFile(row)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteFile(row)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 任務列表 -->
        <el-tab-pane label="任務" name="tasks">
          <el-card class="tab-content-card">
            <template #header>
              <div class="tab-header">
                <span>編輯任務</span>
                <el-button size="small" type="primary" @click="startNewTask">
                  <el-icon><VideoPlay /></el-icon>
                  開始編輯
                </el-button>
              </div>
            </template>

            <div v-if="projectTasks.length === 0" class="empty-state">
              <el-empty description="暫無任務" />
            </div>

            <el-table v-else :data="projectTasks" style="width: 100%">
              <el-table-column prop="id" label="任務ID" width="150">
                <template #default="{ row }">
                  {{ row.id.substring(0, 8) }}...
                </template>
              </el-table-column>
              <el-table-column prop="status" label="狀態" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="進度" width="150">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.progress || 0"
                    :status="getProgressStatus(row.status)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="創建時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="viewTaskDetail(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'completed' && row.output_path"
                      size="small"
                      type="success"
                      @click="downloadResult(row)"
                    >
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'processing' || row.status === 'pending'"
                      size="small"
                      type="warning"
                      @click="cancelTask(row)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 編輯配置 -->
        <el-tab-pane label="編輯配置" name="config">
          <el-card class="tab-content-card">
            <el-form :model="editingConfig" label-width="140px" class="editing-config-form">

              <!-- 視頻片段配置 -->
              <el-divider content-position="left">
                <el-icon><VideoPlay /></el-icon>
                視頻片段配置
              </el-divider>

              <!-- 片頭配置 -->
              <el-form-item label="片頭視頻">
                <div class="segment-config-card">
                  <div class="file-selection-group">
                    <el-select
                      v-model="editingConfig.intro_filename"
                      placeholder="選擇片頭視頻（可選）"
                      clearable
                      class="file-select"
                    >
                      <el-option
                        v-for="file in videoFiles"
                        :key="file.id"
                        :label="file.filename"
                        :value="file.filename"
                      />
                    </el-select>
                    <el-checkbox v-model="editingConfig.intro_enabled" class="enable-checkbox">
                      啟用片頭
                    </el-checkbox>
                  </div>

                  <!-- 片頭音頻配置 -->
                  <div v-if="editingConfig.intro_enabled && editingConfig.intro_filename" class="segment-audio-config">
                    <el-divider content-position="left" class="mini-divider">音頻設置</el-divider>
                    <div class="audio-options">
                      <el-checkbox v-model="editingConfig.intro_mute_audio">靜音原始音頻</el-checkbox>
                      <el-select
                        v-model="editingConfig.intro_replacement_audio"
                        placeholder="替換音頻（可選）"
                        clearable
                        class="audio-select"
                        :disabled="!editingConfig.intro_mute_audio"
                      >
                        <el-option
                          v-for="file in audioFiles"
                          :key="file.id"
                          :label="file.filename"
                          :value="file.filename"
                        />
                      </el-select>
                    </div>
                  </div>

                  <!-- 片頭字幕配置 -->
                  <div v-if="editingConfig.intro_enabled && editingConfig.intro_filename" class="segment-subtitle-config">
                    <el-divider content-position="left" class="mini-divider">字幕設置</el-divider>
                    <div class="subtitle-options">
                      <el-select
                        v-model="editingConfig.intro_subtitle_filename"
                        placeholder="選擇字幕文件（可選）"
                        clearable
                        class="subtitle-select"
                      >
                        <el-option
                          v-for="file in subtitleFiles"
                          :key="file.id"
                          :label="file.filename"
                          :value="file.filename"
                        />
                      </el-select>
                      <el-select
                        v-model="editingConfig.intro_subtitle_effect"
                        placeholder="字幕特效"
                        class="effect-select"
                        :disabled="!editingConfig.intro_subtitle_filename"
                      >
                        <el-option label="無特效" value="none" />
                        <el-option label="卡拉OK效果" value="karaoke" />
                        <el-option label="打字機效果" value="typewriter" />
                        <el-option label="發光效果" value="glow" />
                        <el-option label="跳動效果" value="bounce" />
                        <el-option label="陰影效果" value="shadow" />
                        <el-option label="漸變效果" value="gradient" />
                        <el-option label="彩虹效果" value="rainbow" />
                      </el-select>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <!-- 片中視頻配置（多選） -->
              <el-form-item label="片中視頻" required>
                <div class="middle-videos-config">
                  <el-select
                    v-model="editingConfig.middle_video_filenames"
                    placeholder="選擇片中視頻（可多選）"
                    multiple
                    class="file-select"
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="file in videoFiles"
                      :key="file.id"
                      :label="file.filename"
                      :value="file.filename"
                    />
                  </el-select>
                  <div class="middle-videos-help">
                    <el-text size="small" type="info">
                      可選擇多個視頻文件，將按選擇順序進行拼接。每個視頻可單獨配置音頻和字幕。
                    </el-text>
                  </div>

                  <!-- 每個片中視頻的詳細配置 -->
                  <div v-if="editingConfig.middle_video_filenames.length > 0" class="middle-videos-detail">
                    <el-divider content-position="left" class="mini-divider">片中視頻詳細配置</el-divider>
                    <div
                      v-for="(filename, index) in editingConfig.middle_video_filenames"
                      :key="filename"
                      class="middle-video-item"
                    >
                      <div class="video-item-header">
                        <el-tag type="primary" size="small">{{ index + 1 }}</el-tag>
                        <span class="video-filename">{{ filename }}</span>
                      </div>

                      <!-- 音頻配置 -->
                      <div class="video-item-config">
                        <div class="config-section">
                          <label class="config-label">音頻設置:</label>
                          <el-checkbox
                            v-model="editingConfig.middle_videos_config[filename].mute_audio"
                            @change="ensureMiddleVideoConfig(filename)"
                          >
                            靜音原始音頻
                          </el-checkbox>
                          <el-select
                            v-model="editingConfig.middle_videos_config[filename].replacement_audio"
                            placeholder="替換音頻（可選）"
                            clearable
                            size="small"
                            class="config-select"
                            :disabled="!editingConfig.middle_videos_config[filename].mute_audio"
                          >
                            <el-option
                              v-for="file in audioFiles"
                              :key="file.id"
                              :label="file.filename"
                              :value="file.filename"
                            />
                          </el-select>
                        </div>

                        <!-- 字幕配置 -->
                        <div class="config-section">
                          <label class="config-label">字幕設置:</label>
                          <el-select
                            v-model="editingConfig.middle_videos_config[filename].subtitle_filename"
                            placeholder="選擇字幕文件（可選）"
                            clearable
                            size="small"
                            class="config-select"
                            @change="ensureMiddleVideoConfig(filename)"
                          >
                            <el-option
                              v-for="file in subtitleFiles"
                              :key="file.id"
                              :label="file.filename"
                              :value="file.filename"
                            />
                          </el-select>
                          <el-select
                            v-model="editingConfig.middle_videos_config[filename].subtitle_effect"
                            placeholder="字幕特效"
                            size="small"
                            class="config-select"
                            :disabled="!editingConfig.middle_videos_config[filename].subtitle_filename"
                          >
                            <el-option label="無特效" value="none" />
                            <el-option label="卡拉OK效果" value="karaoke" />
                            <el-option label="打字機效果" value="typewriter" />
                            <el-option label="發光效果" value="glow" />
                            <el-option label="跳動效果" value="bounce" />
                            <el-option label="陰影效果" value="shadow" />
                            <el-option label="漸變效果" value="gradient" />
                            <el-option label="彩虹效果" value="rainbow" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <!-- 片尾配置 -->
              <el-form-item label="片尾視頻">
                <div class="segment-config-card">
                  <div class="file-selection-group">
                    <el-select
                      v-model="editingConfig.outro_filename"
                      placeholder="選擇片尾視頻（可選）"
                      clearable
                      class="file-select"
                    >
                      <el-option
                        v-for="file in videoFiles"
                        :key="file.id"
                        :label="file.filename"
                        :value="file.filename"
                      />
                    </el-select>
                    <el-checkbox v-model="editingConfig.outro_enabled" class="enable-checkbox">
                      啟用片尾
                    </el-checkbox>
                  </div>

                  <!-- 片尾音頻配置 -->
                  <div v-if="editingConfig.outro_enabled && editingConfig.outro_filename" class="segment-audio-config">
                    <el-divider content-position="left" class="mini-divider">音頻設置</el-divider>
                    <div class="audio-options">
                      <el-checkbox v-model="editingConfig.outro_mute_audio">靜音原始音頻</el-checkbox>
                      <el-select
                        v-model="editingConfig.outro_replacement_audio"
                        placeholder="替換音頻（可選）"
                        clearable
                        class="audio-select"
                        :disabled="!editingConfig.outro_mute_audio"
                      >
                        <el-option
                          v-for="file in audioFiles"
                          :key="file.id"
                          :label="file.filename"
                          :value="file.filename"
                        />
                      </el-select>
                    </div>
                  </div>

                  <!-- 片尾字幕配置 -->
                  <div v-if="editingConfig.outro_enabled && editingConfig.outro_filename" class="segment-subtitle-config">
                    <el-divider content-position="left" class="mini-divider">字幕設置</el-divider>
                    <div class="subtitle-options">
                      <el-select
                        v-model="editingConfig.outro_subtitle_filename"
                        placeholder="選擇字幕文件（可選）"
                        clearable
                        class="subtitle-select"
                      >
                        <el-option
                          v-for="file in subtitleFiles"
                          :key="file.id"
                          :label="file.filename"
                          :value="file.filename"
                        />
                      </el-select>
                      <el-select
                        v-model="editingConfig.outro_subtitle_effect"
                        placeholder="字幕特效"
                        class="effect-select"
                        :disabled="!editingConfig.outro_subtitle_filename"
                      >
                        <el-option label="無特效" value="none" />
                        <el-option label="卡拉OK效果" value="karaoke" />
                        <el-option label="打字機效果" value="typewriter" />
                        <el-option label="發光效果" value="glow" />
                        <el-option label="跳動效果" value="bounce" />
                        <el-option label="陰影效果" value="shadow" />
                        <el-option label="漸變效果" value="gradient" />
                        <el-option label="彩虹效果" value="rainbow" />
                      </el-select>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <!-- 音頻配置 -->
              <el-divider content-position="left">
                <el-icon><Microphone /></el-icon>
                音頻配置
              </el-divider>

              <!-- 靜音選項 -->
              <el-form-item label="音頻設置">
                <el-checkbox v-model="editingConfig.mute_original_audio">
                  靜音原始音頻
                </el-checkbox>
              </el-form-item>

              <!-- 音頻替換 -->
              <el-form-item label="替換音頻">
                <div class="file-selection-group">
                  <el-select
                    v-model="editingConfig.replacement_audio_filename"
                    placeholder="選擇替換音頻（可選）"
                    clearable
                    class="file-select"
                    :disabled="!editingConfig.mute_original_audio"
                  >
                    <el-option
                      v-for="file in audioFiles"
                      :key="file.id"
                      :label="file.filename"
                      :value="file.filename"
                    />
                  </el-select>
                  <el-text type="info" size="small" v-if="!editingConfig.mute_original_audio">
                    需要先啟用靜音原始音頻
                  </el-text>
                </div>
              </el-form-item>

              <!-- 背景音樂 -->
              <el-form-item label="背景音樂">
                <el-select
                  v-model="editingConfig.background_music_filename"
                  placeholder="選擇背景音樂（可選）"
                  clearable
                  class="file-select"
                >
                  <el-option
                    v-for="file in audioFiles"
                    :key="file.id"
                    :label="file.filename"
                    :value="file.filename"
                  />
                </el-select>
              </el-form-item>

              <!-- 音量控制 -->
              <el-form-item label="主音頻音量" v-if="!editingConfig.mute_original_audio">
                <el-slider
                  v-model="editingConfig.audio_volume"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  show-input
                  :format-tooltip="(val) => `${Math.round(val * 100)}%`"
                  class="volume-slider"
                />
              </el-form-item>

              <el-form-item label="背景音樂音量" v-if="editingConfig.background_music_filename">
                <el-slider
                  v-model="editingConfig.background_volume"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  show-input
                  :format-tooltip="(val) => `${Math.round(val * 100)}%`"
                  class="volume-slider"
                />
              </el-form-item>

              <!-- 字幕配置 -->
              <el-divider content-position="left">
                <el-icon><ChatLineSquare /></el-icon>
                字幕配置
              </el-divider>

              <!-- 字幕文件選擇 -->
              <el-form-item label="字幕文件">
                <el-select
                  v-model="editingConfig.subtitle_filename"
                  placeholder="選擇字幕文件（可選）"
                  clearable
                  class="file-select"
                >
                  <el-option
                    v-for="file in subtitleFiles"
                    :key="file.id"
                    :label="file.filename"
                    :value="file.filename"
                  />
                </el-select>
              </el-form-item>

              <!-- 字幕特效 -->
              <el-form-item label="字幕特效" v-if="editingConfig.subtitle_filename">
                <el-select v-model="editingConfig.subtitle_effect" placeholder="選擇字幕特效">
                  <el-option label="無特效" value="none" />
                  <el-option label="卡拉OK效果" value="karaoke" />
                  <el-option label="打字機效果" value="typewriter" />
                  <el-option label="發光效果" value="glow" />
                  <el-option label="陰影效果" value="shadow" />
                  <el-option label="漸變效果" value="gradient" />
                  <el-option label="彩虹效果" value="rainbow" />
                </el-select>
                <el-text type="info" size="small" style="margin-top: 8px; display: block;">
                  SRT字幕將自動轉換為ASS格式以支持特效
                </el-text>
              </el-form-item>

              <!-- 視頻輸出配置 -->
              <el-divider content-position="left">
                <el-icon><Monitor /></el-icon>
                視頻輸出配置
              </el-divider>

              <!-- 視頻尺寸 -->
              <el-form-item label="輸出尺寸">
                <el-select v-model="editingConfig.output_size" placeholder="選擇輸出尺寸">
                  <el-option label="保持原始尺寸" value="original" />
                  <!-- 橫向尺寸 (16:9) -->
                  <el-option label="1920x1080 (Full HD)" value="1920x1080" />
                  <el-option label="1280x720 (HD)" value="1280x720" />
                  <el-option label="854x480 (480p)" value="854x480" />
                  <!-- 直向尺寸 (9:16) -->
                  <el-option label="1080x1920 (豎屏 Full HD)" value="1080x1920" />
                  <el-option label="720x1280 (豎屏 HD)" value="720x1280" />
                  <el-option label="480x854 (豎屏 480p)" value="480x854" />
                  <!-- 方形尺寸 (1:1) -->
                  <el-option label="1080x1080 (方形 Full HD)" value="1080x1080" />
                  <el-option label="720x720 (方形 HD)" value="720x720" />
                  <el-option label="480x480 (方形 480p)" value="480x480" />
                  <!-- 4K 尺寸 -->
                  <el-option label="3840x2160 (4K UHD)" value="3840x2160" />
                  <el-option label="2160x3840 (豎屏 4K)" value="2160x3840" />
                  <!-- 其他常用尺寸 -->
                  <el-option label="1440x1080 (4:3)" value="1440x1080" />
                  <el-option label="1024x768 (4:3 標準)" value="1024x768" />
                </el-select>
              </el-form-item>

              <!-- 操作按鈕 -->
              <el-form-item>
                <el-button type="primary" @click="saveConfig">保存配置</el-button>
                <el-button @click="resetConfig">重置配置</el-button>
                <el-button type="success" @click="previewConfig">預覽配置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-else class="error-state">
      <el-result
        icon="warning"
        title="項目不存在"
        sub-title="請檢查項目ID是否正確"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/projects')">
            返回項目列表
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 文件上傳對話框 -->
    <el-dialog v-model="uploadDialogVisible" title="上傳文件" width="500px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadAction"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :accept="acceptedTypes"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖到此處，或<em>點擊上傳</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 mp4, avi, mov, mkv, mp3, wav, aac, srt, ass, vtt 格式文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            上傳
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 編輯項目對話框 -->
    <el-dialog v-model="editDialogVisible" title="編輯項目" width="500px">
      <el-form
        ref="editFormRef"
        :model="editFormData"
        :rules="editFormRules"
        label-width="100px"
      >
        <el-form-item label="項目名稱" prop="name">
          <el-input v-model="editFormData.name" placeholder="請輸入項目名稱" />
        </el-form-item>
        <el-form-item label="項目描述" prop="description">
          <el-input
            v-model="editFormData.description"
            type="textarea"
            rows="3"
            placeholder="請輸入項目描述"
          />
        </el-form-item>
        <el-form-item label="項目狀態" prop="status">
          <el-select v-model="editFormData.status" placeholder="請選擇狀態">
            <el-option label="進行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已歸檔" value="archived" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditProject" :loading="uploading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'
import { projectsAPI } from '@/services/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
  Plus, View, Download, Delete, VideoPlay, Close, UploadFilled,
  Microphone, ChatLineSquare, Monitor
} from '@element-plus/icons-vue'

export default {
  name: 'ProjectDetail',
  components: {
    ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
    Plus, View, Download, Delete, VideoPlay, Close, UploadFilled,
    Microphone, ChatLineSquare, Monitor
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectsStore = useProjectsStore()
    const tasksStore = useTasksStore()

    // 狀態
    const activeTab = ref('files')
    const uploadDialogVisible = ref(false)
    const editDialogVisible = ref(false)
    const uploading = ref(false)
    const uploadRef = ref(null)
    const editFormRef = ref(null)
    const selectedFiles = ref([])

    // 編輯項目表單數據
    const editFormData = reactive({
      id: '',
      name: '',
      description: '',
      status: 'active'
    })

    // 編輯表單驗證規則
    const editFormRules = {
      name: [
        { required: true, message: '請輸入項目名稱', trigger: 'blur' },
        { min: 1, max: 100, message: '項目名稱長度在 1 到 100 個字符', trigger: 'blur' }
      ],
      description: [
        { max: 500, message: '項目描述不能超過 500 個字符', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '請選擇項目狀態', trigger: 'change' }
      ]
    }

    // 編輯配置
    const editingConfig = reactive({
      // 視頻片段配置
      intro_filename: '',
      intro_enabled: false,
      middle_video_filenames: [],
      outro_filename: '',
      outro_enabled: false,

      // 片頭音頻和字幕配置
      intro_mute_audio: false,
      intro_replacement_audio: '',
      intro_subtitle_filename: '',
      intro_subtitle_effect: 'none',

      // 片中視頻詳細配置 (每個視頻的配置)
      middle_videos_config: {},

      // 片尾音頻和字幕配置
      outro_mute_audio: false,
      outro_replacement_audio: '',
      outro_subtitle_filename: '',
      outro_subtitle_effect: 'none',

      // 全局音頻配置
      mute_original_audio: false,
      replacement_audio_filename: '',
      background_music_filename: '',
      audio_volume: 1.0,
      background_volume: 0.3,

      // 全局字幕配置 (保留向後兼容)
      subtitle_filename: '',
      subtitle_effect: 'none',

      // 視頻輸出配置
      output_size: 'original'
    })

    // 計算屬性
    const project = computed(() => projectsStore.currentProject)
    const projectTasks = computed(() =>
      tasksStore.tasks.filter(task => task.project_id === route.params.id)
    )
    const activeTasksCount = computed(() =>
      projectTasks.value.filter(task =>
        task.status === 'pending' || task.status === 'processing'
      ).length
    )
    const completedTasksCount = computed(() =>
      projectTasks.value.filter(task => task.status === 'completed').length
    )
    const uploadAction = computed(() => `/api/uploads/`)
    const acceptedTypes = computed(() => '.mp4,.avi,.mov,.mkv,.mp3,.wav,.aac,.srt,.ass,.vtt')

    // 文件分類計算屬性
    const videoFiles = computed(() =>
      project.value?.files?.filter(f => f.file_type === 'video') || []
    )
    const audioFiles = computed(() =>
      project.value?.files?.filter(f => f.file_type === 'audio') || []
    )
    const subtitleFiles = computed(() =>
      project.value?.files?.filter(f => f.file_type === 'subtitle') || []
    )

    // 方法
    const loadProject = async () => {
      try {
        await projectsStore.fetchProject(route.params.id)
      } catch (error) {
        console.error('Load project error:', error)
        ElMessage.error('加載項目失敗')
      }
    }

    // 進度刷新定時器
    let progressTimer = null

    const loadTasks = async () => {
      try {
        await tasksStore.fetchTasks({ project_id: route.params.id })

        // 檢查是否有進行中的任務，如果有則啟動自動刷新
        const activeTasks = tasksStore.tasks.filter(task =>
          task.status === 'pending' || task.status === 'processing'
        )
        const hasActiveTasks = activeTasks.length > 0

        if (hasActiveTasks && !progressTimer) {
          startProgressRefresh()
        } else if (!hasActiveTasks && progressTimer) {
          stopProgressRefresh()
        }
      } catch (error) {
        console.error('Failed to load tasks:', error)
      }
    }

    const startProgressRefresh = () => {
      if (progressTimer) return

      progressTimer = setInterval(async () => {
        try {
          const currentTab = activeTab.value // 保存當前tab
          await tasksStore.fetchTasks({ project_id: route.params.id })
          activeTab.value = currentTab // 恢復tab狀態
        } catch (error) {
          console.error('Progress refresh error:', error)
        }
      }, 2000) // 每2秒刷新一次
    }

    const stopProgressRefresh = () => {
      if (progressTimer) {
        clearInterval(progressTimer)
        progressTimer = null
      }
    }

    const editProject = () => {
      // 填充編輯表單數據
      editFormData.id = project.value.id
      editFormData.name = project.value.name
      editFormData.description = project.value.description || ''
      editFormData.status = project.value.status
      editDialogVisible.value = true
    }

    const showUploadDialog = () => {
      uploadDialogVisible.value = true
    }

    const handleFileChange = (file, fileList) => {
      selectedFiles.value = fileList
    }

    const beforeUpload = (file) => {
      const maxSize = 500 * 1024 * 1024 // 500MB
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超過 500MB')
        return false
      }
      return true
    }

    const submitEditProject = async () => {
      if (!editFormRef.value) return

      await editFormRef.value.validate(async (valid) => {
        if (valid) {
          uploading.value = true
          try {
            await projectsStore.updateProject(editFormData.id, {
              name: editFormData.name,
              description: editFormData.description,
              status: editFormData.status
            })
            ElMessage.success('項目更新成功')
            editDialogVisible.value = false
            // 重新加載項目數據
            await loadProject()
          } catch (error) {
            ElMessage.error('更新項目失敗')
          } finally {
            uploading.value = false
          }
        }
      })
    }

    const submitUpload = async () => {
      if (!selectedFiles.value.length) {
        ElMessage.warning('請選擇要上傳的文件')
        return
      }

      uploading.value = true
      try {
        // 創建 FormData
        const formData = new FormData()

        // 添加項目ID
        formData.append('project_id', route.params.id)

        // 添加文件
        selectedFiles.value.forEach(file => {
          formData.append('files', file.raw || file)
        })

        // 調用上傳 API
        const response = await fetch('/api/uploads/', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const result = await response.json()
          ElMessage.success(`成功上傳 ${result.uploaded_files?.length || 0} 個文件`)
          uploadDialogVisible.value = false
          // 清空文件列表
          selectedFiles.value = []
          if (uploadRef.value && uploadRef.value.clearFiles) {
            uploadRef.value.clearFiles()
          }
          // 重新加載項目數據
          await loadProject()
        } else {
          const error = await response.json()
          ElMessage.error(error.detail || '上傳失敗')
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('上傳失敗')
      } finally {
        uploading.value = false
      }
    }

    const previewFile = (file) => {
      // 對於視頻文件，可以在新窗口中打開
      if (file.file_type === 'video') {
        window.open(`/api/files/${file.id}/preview`, '_blank')
      } else {
        ElMessage.info('該文件類型暫不支持預覽')
      }
    }

    const downloadFile = (file) => {
      // 創建下載鏈接
      const link = document.createElement('a')
      link.href = `/api/files/${file.id}/download`
      link.download = file.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const deleteFile = async (file) => {
      try {
        await ElMessageBox.confirm(
          `確定要刪除文件 "${file.filename}" 嗎？此操作不可恢復。`,
          '確認刪除',
          {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // 調用刪除 API
        const response = await fetch(`/api/files/${file.id}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          ElMessage.success('文件刪除成功')
          // 重新加載項目數據
          await loadProject()
        } else {
          const error = await response.json()
          ElMessage.error(error.detail || '刪除失敗')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Delete file error:', error)
          ElMessage.error('刪除失敗')
        }
      }
    }

    const startNewTask = async () => {
      try {
        // 檢查片中視頻是否已選擇
        if (!editingConfig.middle_video_filenames || editingConfig.middle_video_filenames.length === 0) {
          ElMessage.warning('請先在編輯配置中選擇至少一個片中視頻文件')
          activeTab.value = 'config'
          return
        }

        // 檢查是否有正在進行的任務
        const activeTasks = projectTasks.value.filter(task =>
          task.status === 'pending' || task.status === 'processing'
        )
        if (activeTasks.length > 0) {
          ElMessage.warning('該項目已有正在進行的編輯任務，請等待完成後再開始新任務')
          return
        }

        // 構建編輯配置
        const editingData = {
          project_id: project.value.id,
          middle_video_filenames: editingConfig.middle_video_filenames,
          output_size: editingConfig.output_size,
          audio_volume: editingConfig.audio_volume,
          background_volume: editingConfig.background_volume
        }

        // 添加片頭片尾
        if (editingConfig.intro_enabled && editingConfig.intro_filename) {
          editingData.intro_filename = editingConfig.intro_filename
        }
        if (editingConfig.outro_enabled && editingConfig.outro_filename) {
          editingData.outro_filename = editingConfig.outro_filename
        }

        // 添加音頻設置
        if (editingConfig.mute_original_audio) {
          editingData.mute_original_audio = true
          if (editingConfig.replacement_audio_filename) {
            editingData.replacement_audio_filename = editingConfig.replacement_audio_filename
          }
        }
        if (editingConfig.background_music_filename) {
          editingData.background_music_filename = editingConfig.background_music_filename
        }

        // 添加字幕設置
        if (editingConfig.subtitle_filename) {
          editingData.subtitle_filename = editingConfig.subtitle_filename
          editingData.subtitle_effect = editingConfig.subtitle_effect
        }

        // 構建確認信息
        const configInfo = []

        if (editingConfig.intro_enabled && editingConfig.intro_filename) {
          configInfo.push(`片頭：${editingConfig.intro_filename}`)
        }

        configInfo.push(`片中視頻：${editingConfig.middle_video_filenames.join(', ')}`)

        if (editingConfig.outro_enabled && editingConfig.outro_filename) {
          configInfo.push(`片尾：${editingConfig.outro_filename}`)
        }

        if (editingConfig.mute_original_audio) {
          configInfo.push('音頻：原始音頻已靜音')
          if (editingConfig.replacement_audio_filename) {
            configInfo.push(`替換音頻：${editingConfig.replacement_audio_filename}`)
          }
        }

        if (editingConfig.background_music_filename) {
          configInfo.push(`背景音樂：${editingConfig.background_music_filename}`)
        }

        if (editingConfig.subtitle_filename) {
          configInfo.push(`字幕：${editingConfig.subtitle_filename} (${editingConfig.subtitle_effect})`)
        }

        configInfo.push(`輸出尺寸：${editingConfig.output_size}`)

        // 顯示確認對話框
        await ElMessageBox.confirm(
          `確定要開始編輯任務嗎？\n\n${configInfo.join('\n')}`,
          '開始編輯任務',
          {
            confirmButtonText: '開始編輯',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        // 開始編輯任務
        uploading.value = true
        const response = await tasksStore.startEditing(editingData)

        ElMessage.success(`編輯任務已創建！任務ID: ${response.job_id}`)

        // 刷新任務列表
        await loadTasks()

        // 切換到任務標籤頁
        activeTab.value = 'tasks'

      } catch (error) {
        if (error !== 'cancel') {
          console.error('Start editing error:', error)
          ElMessage.error('開始編輯任務失敗')
        }
      } finally {
        uploading.value = false
      }
    }

    const viewTaskDetail = (task) => {
      router.push('/tasks')
    }

    const downloadResult = (task) => {
      ElMessage.info('下載結果功能待實現')
    }

    const cancelTask = async (task) => {
      try {
        await tasksStore.cancelTask(task.id)
        ElMessage.success('任務已取消')
        loadTasks()
      } catch (error) {
        ElMessage.error('取消任務失敗')
      }
    }

    const saveConfig = () => {
      // 這裡可以添加保存到本地存儲或後端的邏輯
      localStorage.setItem(`editing_config_${route.params.id}`, JSON.stringify(editingConfig))
      ElMessage.success('編輯配置已保存')
    }

    // 確保片中視頻配置存在
    const ensureMiddleVideoConfig = (filename) => {
      if (!editingConfig.middle_videos_config[filename]) {
        editingConfig.middle_videos_config[filename] = {
          mute_audio: false,
          replacement_audio: '',
          subtitle_filename: '',
          subtitle_effect: 'none'
        }
      }
    }

    // 監聽片中視頻選擇變化，自動初始化配置
    watch(() => editingConfig.middle_video_filenames, (newFilenames, oldFilenames) => {
      // 為新選擇的視頻初始化配置
      newFilenames.forEach(filename => {
        ensureMiddleVideoConfig(filename)
      })

      // 清理不再選擇的視頻配置
      if (oldFilenames) {
        oldFilenames.forEach(filename => {
          if (!newFilenames.includes(filename)) {
            delete editingConfig.middle_videos_config[filename]
          }
        })
      }
    }, { deep: true })

    const resetConfig = () => {
      // 重置所有配置到默認值
      editingConfig.intro_filename = ''
      editingConfig.intro_enabled = false
      editingConfig.middle_video_filenames = []
      editingConfig.outro_filename = ''
      editingConfig.outro_enabled = false

      // 重置片頭配置
      editingConfig.intro_mute_audio = false
      editingConfig.intro_replacement_audio = ''
      editingConfig.intro_subtitle_filename = ''
      editingConfig.intro_subtitle_effect = 'none'

      // 重置片中配置
      editingConfig.middle_videos_config = {}

      // 重置片尾配置
      editingConfig.outro_mute_audio = false
      editingConfig.outro_replacement_audio = ''
      editingConfig.outro_subtitle_filename = ''
      editingConfig.outro_subtitle_effect = 'none'

      // 重置全局配置
      editingConfig.mute_original_audio = false
      editingConfig.replacement_audio_filename = ''
      editingConfig.background_music_filename = ''
      editingConfig.audio_volume = 1.0
      editingConfig.background_volume = 0.3

      editingConfig.subtitle_filename = ''
      editingConfig.subtitle_effect = 'none'

      editingConfig.output_size = 'original'

      ElMessage.success('配置已重置')
    }

    const previewConfig = () => {
      // 顯示當前配置的預覽
      const configSummary = []

      // 視頻片段
      if (editingConfig.intro_enabled && editingConfig.intro_filename) {
        configSummary.push(`片頭: ${editingConfig.intro_filename}`)
      }

      if (editingConfig.middle_video_filenames && editingConfig.middle_video_filenames.length > 0) {
        configSummary.push(`片中視頻: ${editingConfig.middle_video_filenames.join(', ')}`)
      } else {
        configSummary.push('片中視頻: 未選擇')
      }

      if (editingConfig.outro_enabled && editingConfig.outro_filename) {
        configSummary.push(`片尾: ${editingConfig.outro_filename}`)
      }

      // 音頻設置
      if (editingConfig.mute_original_audio) {
        configSummary.push('原始音頻: 已靜音')
        if (editingConfig.replacement_audio_filename) {
          configSummary.push(`替換音頻: ${editingConfig.replacement_audio_filename}`)
        }
      } else {
        configSummary.push(`原始音頻音量: ${Math.round(editingConfig.audio_volume * 100)}%`)
      }

      if (editingConfig.background_music_filename) {
        configSummary.push(`背景音樂: ${editingConfig.background_music_filename} (${Math.round(editingConfig.background_volume * 100)}%)`)
      }

      // 字幕設置
      if (editingConfig.subtitle_filename) {
        configSummary.push(`字幕: ${editingConfig.subtitle_filename} (${editingConfig.subtitle_effect})`)
      }

      // 輸出設置
      configSummary.push(`輸出尺寸: ${editingConfig.output_size}`)

      ElMessageBox.alert(
        configSummary.join('\n'),
        '編輯配置預覽',
        {
          confirmButtonText: '確定',
          type: 'info'
        }
      )
    }

    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-TW')
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '-'
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getStatusType = (status) => {
      const statusMap = {
        'active': 'success',
        'completed': 'info',
        'archived': 'warning',
        'pending': 'info',
        'processing': 'warning',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '進行中',
        'completed': '已完成',
        'archived': '已歸檔',
        'pending': '等待中',
        'processing': '處理中',
        'failed': '失敗'
      }
      return statusMap[status] || status
    }

    const getFileTypeColor = (type) => {
      const colorMap = {
        'video': 'primary',
        'audio': 'success',
        'subtitle': 'warning'
      }
      return colorMap[type] || 'info'
    }

    const getFileTypeText = (type) => {
      const typeMap = {
        'video': '視頻',
        'audio': '音頻',
        'subtitle': '字幕'
      }
      return typeMap[type] || type
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return undefined
    }

    // 監聽路由變化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadProject()
        loadTasks()
      }
    }, { immediate: true })

    // 監聽項目變化，自動設置主視頻
    watch(project, (newProject) => {
      if (newProject && newProject.files) {
        const videos = newProject.files.filter(f => f.file_type === 'video')
        if (videos.length > 0 && editingConfig.middle_video_filenames.length === 0) {
          editingConfig.middle_video_filenames = [videos[0].filename]
        }

        // 嘗試從本地存儲恢復配置
        const savedConfig = localStorage.getItem(`editing_config_${route.params.id}`)
        if (savedConfig) {
          try {
            const config = JSON.parse(savedConfig)
            Object.assign(editingConfig, config)
          } catch (e) {
            console.warn('Failed to load saved config:', e)
          }
        }
      }
    }, { immediate: true })

    onMounted(() => {
      loadProject()
      loadTasks()
    })

    onUnmounted(() => {
      // 清理定時器
      stopProgressRefresh()
    })

    return {
      projectsStore,
      project,
      projectTasks,
      activeTasksCount,
      completedTasksCount,
      activeTab,
      uploadDialogVisible,
      editDialogVisible,
      uploading,
      uploadRef,
      editFormRef,
      selectedFiles,
      editFormData,
      editFormRules,
      editingConfig,
      uploadAction,
      acceptedTypes,
      editProject,
      submitEditProject,
      showUploadDialog,
      handleFileChange,
      beforeUpload,
      submitUpload,
      previewFile,
      downloadFile,
      deleteFile,
      startNewTask,
      viewTaskDetail,
      downloadResult,
      cancelTask,
      saveConfig,
      resetConfig,
      previewConfig,
      videoFiles,
      audioFiles,
      subtitleFiles,
      formatDate,
      formatFileSize,
      getStatusType,
      getStatusText,
      getFileTypeColor,
      getFileTypeText,
      getProgressStatus
    }
  }
}
</script>

<style scoped>
.project-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.project-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.project-info p {
  margin: 0 0 12px 0;
  color: #606266;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.project-stats {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.files {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.project-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-top: 20px;
  overflow: hidden;
}

/* Tab 導航樣式優化 */
.project-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.project-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
  background: transparent;
}

.project-tabs :deep(.el-tabs__nav-scroll) {
  display: flex;
  justify-content: center;
  background: transparent;
}

.project-tabs :deep(.el-tabs__nav) {
  border: none;
  background: transparent;
  display: flex;
  justify-content: center;
}

.project-tabs :deep(.el-tabs__item) {
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  font-size: 15px;
  padding: 0 32px;
  height: 56px;
  line-height: 56px;
  margin: 0;
  background: transparent;
  transition: all 0.3s ease;
  position: relative;
  min-width: 120px;
  text-align: center;
}

.project-tabs :deep(.el-tabs__item:hover) {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.project-tabs :deep(.el-tabs__item.is-active) {
  color: white;
  background: rgba(255, 255, 255, 0.15);
  font-weight: 600;
}

.project-tabs :deep(.el-tabs__active-bar) {
  background: white;
  height: 3px;
  border-radius: 2px;
}

.project-tabs :deep(.el-tabs__content) {
  padding: 24px;
  background: #f8fafc;
  min-height: 400px;
}

/* Tab 內容卡片樣式 */
.tab-content-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
  transition: all 0.3s ease;
}

.tab-content-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tab-content-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
}

.tab-content-card :deep(.el-card__body) {
  padding: 24px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-header span {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.tab-header .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-header .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .project-tabs :deep(.el-tabs__item) {
    padding: 0 16px;
    font-size: 14px;
    min-width: 80px;
  }

  .project-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }

  .tab-content-card :deep(.el-card__header) {
    padding: 16px 20px;
  }

  .tab-content-card :deep(.el-card__body) {
    padding: 20px;
  }

  .tab-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 表格樣式優化 */
.project-tabs :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.project-tabs :deep(.el-table__header) {
  background: #f8fafc;
}

.project-tabs :deep(.el-table th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.project-tabs :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

.project-tabs :deep(.el-table__row:hover) {
  background: #f8fafc;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.error-state {
  text-align: center;
  padding: 60px;
}

/* 編輯配置表單樣式 */
.editing-config-form {
  max-width: 800px;
}

.editing-config-form .el-divider {
  margin: 24px 0 16px 0;
  font-weight: 600;
  color: #374151;
}

.editing-config-form .el-divider .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.file-selection-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.middle-videos-config {
  width: 100%;
}

.middle-videos-help {
  margin-top: 8px;
}

.file-select {
  flex: 1;
  min-width: 200px;
}

.enable-checkbox {
  white-space: nowrap;
}

.volume-slider {
  width: 100%;
}

.editing-config-form .el-form-item__label {
  font-weight: 500;
  color: #374151;
}

.editing-config-form .el-select,
.editing-config-form .el-slider {
  width: 100%;
}

.editing-config-form .el-text {
  font-size: 12px;
  margin-top: 4px;
}

/* 配置分組樣式 */
.editing-config-form .el-form-item {
  margin-bottom: 20px;
}

.editing-config-form .el-form-item:last-child {
  margin-bottom: 0;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .file-selection-group {
    flex-direction: column;
    align-items: stretch;
  }

  .file-select {
    min-width: unset;
  }

  .editing-config-form {
    max-width: 100%;
  }
}
</style>
