<template>
  <div class="project-detail">
    <div v-if="projectsStore.loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="project" class="project-content">
      <!-- 項目頭部 -->
      <div class="project-header">
        <div class="header-left">
          <el-button @click="$router.back()" circle>
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <div class="project-info">
            <h1>{{ project.name }}</h1>
            <p>{{ project.description || '無描述' }}</p>
            <div class="project-meta">
              <el-tag :type="getStatusType(project.status)">
                {{ getStatusText(project.status) }}
              </el-tag>
              <span>創建於 {{ formatDate(project.created_at) }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="editProject">
            <el-icon><Edit /></el-icon>
            編輯項目
          </el-button>
          <el-button type="primary" @click="showUploadDialog">
            <el-icon><Upload /></el-icon>
            上傳文件
          </el-button>
        </div>
      </div>

      <!-- 項目統計 -->
      <div class="project-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon files">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ project.files?.length || 0 }}</div>
                  <div class="stat-label">文件數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon tasks">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ projectTasks.length }}</div>
                  <div class="stat-label">任務數量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon processing">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ activeTasksCount }}</div>
                  <div class="stat-label">處理中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ completedTasksCount }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 標籤頁 -->
      <el-tabs v-model="activeTab" class="project-tabs" tab-position="top">
        <!-- 文件列表 -->
        <el-tab-pane label="文件" name="files">
          <el-card class="tab-content-card">
            <template #header>
              <div class="tab-header">
                <span>項目文件</span>
                <el-button size="small" @click="showUploadDialog">
                  <el-icon><Plus /></el-icon>
                  添加文件
                </el-button>
              </div>
            </template>

            <div v-if="!project.files || project.files.length === 0" class="empty-state">
              <el-empty description="暫無文件" />
            </div>

            <el-table v-else :data="project.files" style="width: 100%">
              <el-table-column prop="filename" label="文件名" min-width="200" />
              <el-table-column prop="file_type" label="類型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getFileTypeColor(row.file_type)">
                    {{ getFileTypeText(row.file_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="file_size" label="大小" width="120">
                <template #default="{ row }">
                  {{ formatFileSize(row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="uploaded_at" label="上傳時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.uploaded_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="previewFile(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" @click="downloadFile(row)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteFile(row)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 任務列表 -->
        <el-tab-pane label="任務" name="tasks">
          <el-card class="tab-content-card">
            <template #header>
              <div class="tab-header">
                <span>編輯任務</span>
                <el-button size="small" type="primary" @click="startNewTask">
                  <el-icon><VideoPlay /></el-icon>
                  開始編輯
                </el-button>
              </div>
            </template>

            <div v-if="projectTasks.length === 0" class="empty-state">
              <el-empty description="暫無任務" />
            </div>

            <el-table v-else :data="projectTasks" style="width: 100%">
              <el-table-column prop="id" label="任務ID" width="150">
                <template #default="{ row }">
                  {{ row.id.substring(0, 8) }}...
                </template>
              </el-table-column>
              <el-table-column prop="status" label="狀態" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="進度" width="150">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.progress || 0"
                    :status="getProgressStatus(row.status)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="創建時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button-group>
                    <el-button size="small" @click="viewTaskDetail(row)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'completed' && row.output_path"
                      size="small"
                      type="success"
                      @click="downloadResult(row)"
                    >
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button
                      v-if="row.status === 'processing' || row.status === 'pending'"
                      size="small"
                      type="warning"
                      @click="cancelTask(row)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 編輯配置 -->
        <el-tab-pane label="編輯配置" name="config">
          <el-card class="tab-content-card">
            <el-form :model="editingConfig" label-width="120px">
              <el-form-item label="字幕效果">
                <el-select v-model="editingConfig.subtitle_effect" placeholder="選擇字幕效果">
                  <el-option label="無效果" value="none" />
                  <el-option label="卡拉OK" value="karaoke" />
                  <el-option label="打字機" value="typewriter" />
                  <el-option label="發光" value="glow" />
                  <el-option label="陰影" value="shadow" />
                </el-select>
              </el-form-item>
              <el-form-item label="背景音樂音量">
                <el-slider
                  v-model="editingConfig.background_volume"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  show-input
                />
              </el-form-item>
              <el-form-item label="音頻音量">
                <el-slider
                  v-model="editingConfig.audio_volume"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  show-input
                />
              </el-form-item>
              <el-form-item label="視頻尺寸">
                <el-select v-model="editingConfig.output_size" placeholder="選擇輸出尺寸">
                  <el-option label="保持原始" value="original" />
                  <!-- 橫向尺寸 (16:9) -->
                  <el-option label="1920x1080 (Full HD)" value="1920x1080" />
                  <el-option label="1280x720 (HD)" value="1280x720" />
                  <el-option label="854x480 (480p)" value="854x480" />
                  <!-- 直向尺寸 (9:16) -->
                  <el-option label="1080x1920 (豎屏 Full HD)" value="1080x1920" />
                  <el-option label="720x1280 (豎屏 HD)" value="720x1280" />
                  <el-option label="480x854 (豎屏 480p)" value="480x854" />
                  <!-- 方形尺寸 (1:1) -->
                  <el-option label="1080x1080 (方形 Full HD)" value="1080x1080" />
                  <el-option label="720x720 (方形 HD)" value="720x720" />
                  <el-option label="480x480 (方形 480p)" value="480x480" />
                  <!-- 4K 尺寸 -->
                  <el-option label="3840x2160 (4K UHD)" value="3840x2160" />
                  <el-option label="2160x3840 (豎屏 4K)" value="2160x3840" />
                  <!-- 其他常用尺寸 -->
                  <el-option label="1440x1080 (4:3)" value="1440x1080" />
                  <el-option label="1024x768 (4:3 標準)" value="1024x768" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveConfig">保存配置</el-button>
                <el-button @click="resetConfig">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-else class="error-state">
      <el-result
        icon="warning"
        title="項目不存在"
        sub-title="請檢查項目ID是否正確"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/projects')">
            返回項目列表
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 文件上傳對話框 -->
    <el-dialog v-model="uploadDialogVisible" title="上傳文件" width="500px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadAction"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :accept="acceptedTypes"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖到此處，或<em>點擊上傳</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 mp4, avi, mov, mkv, mp3, wav, aac, srt, ass, vtt 格式文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            上傳
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 編輯項目對話框 -->
    <el-dialog v-model="editDialogVisible" title="編輯項目" width="500px">
      <el-form
        ref="editFormRef"
        :model="editFormData"
        :rules="editFormRules"
        label-width="100px"
      >
        <el-form-item label="項目名稱" prop="name">
          <el-input v-model="editFormData.name" placeholder="請輸入項目名稱" />
        </el-form-item>
        <el-form-item label="項目描述" prop="description">
          <el-input
            v-model="editFormData.description"
            type="textarea"
            rows="3"
            placeholder="請輸入項目描述"
          />
        </el-form-item>
        <el-form-item label="項目狀態" prop="status">
          <el-select v-model="editFormData.status" placeholder="請選擇狀態">
            <el-option label="進行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已歸檔" value="archived" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditProject" :loading="uploading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'
import { projectsAPI } from '@/services/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
  Plus, View, Download, Delete, VideoPlay, Close, UploadFilled
} from '@element-plus/icons-vue'

export default {
  name: 'ProjectDetail',
  components: {
    ArrowLeft, Edit, Upload, Document, Setting, Loading, Check,
    Plus, View, Download, Delete, VideoPlay, Close, UploadFilled
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectsStore = useProjectsStore()
    const tasksStore = useTasksStore()

    // 狀態
    const activeTab = ref('files')
    const uploadDialogVisible = ref(false)
    const editDialogVisible = ref(false)
    const uploading = ref(false)
    const uploadRef = ref(null)
    const editFormRef = ref(null)
    const selectedFiles = ref([])

    // 編輯項目表單數據
    const editFormData = reactive({
      id: '',
      name: '',
      description: '',
      status: 'active'
    })

    // 編輯表單驗證規則
    const editFormRules = {
      name: [
        { required: true, message: '請輸入項目名稱', trigger: 'blur' },
        { min: 1, max: 100, message: '項目名稱長度在 1 到 100 個字符', trigger: 'blur' }
      ],
      description: [
        { max: 500, message: '項目描述不能超過 500 個字符', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '請選擇項目狀態', trigger: 'change' }
      ]
    }

    // 編輯配置
    const editingConfig = reactive({
      subtitle_effect: 'none',
      background_volume: 0.3,
      audio_volume: 1.0,
      output_size: 'original'
    })

    // 計算屬性
    const project = computed(() => projectsStore.currentProject)
    const projectTasks = computed(() => 
      tasksStore.tasks.filter(task => task.project_id === route.params.id)
    )
    const activeTasksCount = computed(() => 
      projectTasks.value.filter(task => 
        task.status === 'pending' || task.status === 'processing'
      ).length
    )
    const completedTasksCount = computed(() => 
      projectTasks.value.filter(task => task.status === 'completed').length
    )
    const uploadAction = computed(() => `/api/uploads/`)
    const acceptedTypes = computed(() => '.mp4,.avi,.mov,.mkv,.mp3,.wav,.aac,.srt,.ass,.vtt')

    // 方法
    const loadProject = async () => {
      try {
        await projectsStore.fetchProject(route.params.id)
      } catch (error) {
        console.error('Load project error:', error)
        ElMessage.error('加載項目失敗')
      }
    }

    const loadTasks = async () => {
      try {
        await tasksStore.fetchTasks({ project_id: route.params.id })
      } catch (error) {
        console.error('Failed to load tasks:', error)
      }
    }

    const editProject = () => {
      // 填充編輯表單數據
      editFormData.id = project.value.id
      editFormData.name = project.value.name
      editFormData.description = project.value.description || ''
      editFormData.status = project.value.status
      editDialogVisible.value = true
    }

    const showUploadDialog = () => {
      uploadDialogVisible.value = true
    }

    const handleFileChange = (file, fileList) => {
      selectedFiles.value = fileList
    }

    const beforeUpload = (file) => {
      const maxSize = 500 * 1024 * 1024 // 500MB
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超過 500MB')
        return false
      }
      return true
    }

    const submitEditProject = async () => {
      if (!editFormRef.value) return

      await editFormRef.value.validate(async (valid) => {
        if (valid) {
          uploading.value = true
          try {
            await projectsStore.updateProject(editFormData.id, {
              name: editFormData.name,
              description: editFormData.description,
              status: editFormData.status
            })
            ElMessage.success('項目更新成功')
            editDialogVisible.value = false
            // 重新加載項目數據
            await loadProject()
          } catch (error) {
            ElMessage.error('更新項目失敗')
          } finally {
            uploading.value = false
          }
        }
      })
    }

    const submitUpload = async () => {
      if (!selectedFiles.value.length) {
        ElMessage.warning('請選擇要上傳的文件')
        return
      }

      uploading.value = true
      try {
        // 創建 FormData
        const formData = new FormData()

        // 添加項目ID
        formData.append('project_id', route.params.id)

        // 添加文件
        selectedFiles.value.forEach(file => {
          formData.append('files', file.raw || file)
        })

        // 調用上傳 API
        const response = await fetch('/api/uploads/', {
          method: 'POST',
          body: formData
        })

        if (response.ok) {
          const result = await response.json()
          ElMessage.success(`成功上傳 ${result.uploaded_files?.length || 0} 個文件`)
          uploadDialogVisible.value = false
          // 清空文件列表
          selectedFiles.value = []
          if (uploadRef.value && uploadRef.value.clearFiles) {
            uploadRef.value.clearFiles()
          }
          // 重新加載項目數據
          await loadProject()
        } else {
          const error = await response.json()
          ElMessage.error(error.detail || '上傳失敗')
        }
      } catch (error) {
        console.error('Upload error:', error)
        ElMessage.error('上傳失敗')
      } finally {
        uploading.value = false
      }
    }

    const previewFile = (file) => {
      // 對於視頻文件，可以在新窗口中打開
      if (file.file_type === 'video') {
        window.open(`/api/files/${file.id}/preview`, '_blank')
      } else {
        ElMessage.info('該文件類型暫不支持預覽')
      }
    }

    const downloadFile = (file) => {
      // 創建下載鏈接
      const link = document.createElement('a')
      link.href = `/api/files/${file.id}/download`
      link.download = file.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const deleteFile = async (file) => {
      try {
        await ElMessageBox.confirm(
          `確定要刪除文件 "${file.filename}" 嗎？此操作不可恢復。`,
          '確認刪除',
          {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // 調用刪除 API
        const response = await fetch(`/api/files/${file.id}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          ElMessage.success('文件刪除成功')
          // 重新加載項目數據
          await loadProject()
        } else {
          const error = await response.json()
          ElMessage.error(error.detail || '刪除失敗')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Delete file error:', error)
          ElMessage.error('刪除失敗')
        }
      }
    }

    const startNewTask = async () => {
      try {
        // 檢查是否有視頻文件
        const videoFiles = project.value.files?.filter(f => f.file_type === 'video') || []
        if (videoFiles.length === 0) {
          ElMessage.warning('請先上傳視頻文件才能開始編輯')
          return
        }

        // 檢查是否有正在進行的任務
        const activeTasks = projectTasks.value.filter(task =>
          task.status === 'pending' || task.status === 'processing'
        )
        if (activeTasks.length > 0) {
          ElMessage.warning('該項目已有正在進行的編輯任務，請等待完成後再開始新任務')
          return
        }

        // 使用第一個視頻文件作為主視頻
        const mainVideo = videoFiles[0]
        const audioFiles = project.value.files?.filter(f => f.file_type === 'audio') || []
        const subtitleFiles = project.value.files?.filter(f => f.file_type === 'subtitle') || []

        // 構建編輯配置 - 讓後端處理路徑解析
        const editingData = {
          project_id: project.value.id,
          main_video_filename: mainVideo.filename,
          ...editingConfig
        }

        // 添加音頻文件（如果有）
        if (audioFiles.length > 0) {
          editingData.background_music_filename = audioFiles[0].filename
        }

        // 添加字幕文件（如果有）
        if (subtitleFiles.length > 0) {
          editingData.subtitle_filename = subtitleFiles[0].filename
        }

        // 顯示確認對話框
        await ElMessageBox.confirm(
          `確定要開始編輯任務嗎？\n\n主視頻：${mainVideo.filename}\n音頻文件：${audioFiles.length > 0 ? audioFiles[0].filename : '無'}\n字幕文件：${subtitleFiles.length > 0 ? subtitleFiles[0].filename : '無'}\n\n編輯配置將使用當前的設置。`,
          '開始編輯任務',
          {
            confirmButtonText: '開始編輯',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        // 開始編輯任務
        uploading.value = true
        const response = await tasksStore.startEditing(editingData)

        ElMessage.success(`編輯任務已創建！任務ID: ${response.job_id}`)

        // 刷新任務列表
        await loadTasks()

        // 切換到任務標籤頁
        activeTab.value = 'tasks'

      } catch (error) {
        if (error !== 'cancel') {
          console.error('Start editing error:', error)
          ElMessage.error('開始編輯任務失敗')
        }
      } finally {
        uploading.value = false
      }
    }

    const viewTaskDetail = (task) => {
      router.push('/tasks')
    }

    const downloadResult = (task) => {
      ElMessage.info('下載結果功能待實現')
    }

    const cancelTask = async (task) => {
      try {
        await tasksStore.cancelTask(task.id)
        ElMessage.success('任務已取消')
        loadTasks()
      } catch (error) {
        ElMessage.error('取消任務失敗')
      }
    }

    const saveConfig = () => {
      ElMessage.success('配置已保存')
    }

    const resetConfig = () => {
      editingConfig.subtitle_effect = 'none'
      editingConfig.background_volume = 0.3
      editingConfig.audio_volume = 1.0
      editingConfig.output_size = 'original'
    }

    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-TW')
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '-'
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getStatusType = (status) => {
      const statusMap = {
        'active': 'success',
        'completed': 'info',
        'archived': 'warning',
        'pending': 'info',
        'processing': 'warning',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '進行中',
        'completed': '已完成',
        'archived': '已歸檔',
        'pending': '等待中',
        'processing': '處理中',
        'failed': '失敗'
      }
      return statusMap[status] || status
    }

    const getFileTypeColor = (type) => {
      const colorMap = {
        'video': 'primary',
        'audio': 'success',
        'subtitle': 'warning'
      }
      return colorMap[type] || 'info'
    }

    const getFileTypeText = (type) => {
      const typeMap = {
        'video': '視頻',
        'audio': '音頻',
        'subtitle': '字幕'
      }
      return typeMap[type] || type
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return undefined
    }

    // 監聽路由變化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadProject()
        loadTasks()
      }
    }, { immediate: true })

    onMounted(() => {
      loadProject()
      loadTasks()
    })

    return {
      projectsStore,
      project,
      projectTasks,
      activeTasksCount,
      completedTasksCount,
      activeTab,
      uploadDialogVisible,
      editDialogVisible,
      uploading,
      uploadRef,
      editFormRef,
      selectedFiles,
      editFormData,
      editFormRules,
      editingConfig,
      uploadAction,
      acceptedTypes,
      editProject,
      submitEditProject,
      showUploadDialog,
      handleFileChange,
      beforeUpload,
      submitUpload,
      previewFile,
      downloadFile,
      deleteFile,
      startNewTask,
      viewTaskDetail,
      downloadResult,
      cancelTask,
      saveConfig,
      resetConfig,
      formatDate,
      formatFileSize,
      getStatusType,
      getStatusText,
      getFileTypeColor,
      getFileTypeText,
      getProgressStatus
    }
  }
}
</script>

<style scoped>
.project-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.project-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.project-info p {
  margin: 0 0 12px 0;
  color: #606266;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.project-stats {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.files {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.project-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-top: 20px;
  overflow: hidden;
}

/* Tab 導航樣式優化 */
.project-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.project-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
  background: transparent;
}

.project-tabs :deep(.el-tabs__nav-scroll) {
  display: flex;
  justify-content: center;
  background: transparent;
}

.project-tabs :deep(.el-tabs__nav) {
  border: none;
  background: transparent;
  display: flex;
  justify-content: center;
}

.project-tabs :deep(.el-tabs__item) {
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  font-size: 15px;
  padding: 0 32px;
  height: 56px;
  line-height: 56px;
  margin: 0;
  background: transparent;
  transition: all 0.3s ease;
  position: relative;
  min-width: 120px;
  text-align: center;
}

.project-tabs :deep(.el-tabs__item:hover) {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.project-tabs :deep(.el-tabs__item.is-active) {
  color: white;
  background: rgba(255, 255, 255, 0.15);
  font-weight: 600;
}

.project-tabs :deep(.el-tabs__active-bar) {
  background: white;
  height: 3px;
  border-radius: 2px;
}

.project-tabs :deep(.el-tabs__content) {
  padding: 24px;
  background: #f8fafc;
  min-height: 400px;
}

/* Tab 內容卡片樣式 */
.tab-content-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: white;
  transition: all 0.3s ease;
}

.tab-content-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tab-content-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
}

.tab-content-card :deep(.el-card__body) {
  padding: 24px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-header span {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.tab-header .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-header .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .project-tabs :deep(.el-tabs__item) {
    padding: 0 16px;
    font-size: 14px;
    min-width: 80px;
  }

  .project-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }

  .tab-content-card :deep(.el-card__header) {
    padding: 16px 20px;
  }

  .tab-content-card :deep(.el-card__body) {
    padding: 20px;
  }

  .tab-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 表格樣式優化 */
.project-tabs :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.project-tabs :deep(.el-table__header) {
  background: #f8fafc;
}

.project-tabs :deep(.el-table th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.project-tabs :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

.project-tabs :deep(.el-table__row:hover) {
  background: #f8fafc;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.error-state {
  text-align: center;
  padding: 60px;
}
</style>
