#!/usr/bin/env python3
"""
检查项目编辑配置和任务历史
"""
import requests
import json

def check_project_config():
    """检查项目配置"""
    project_id = 'e26a251a-0e00-4db7-a3fc-24b0341e2277'
    base_url = 'http://localhost:8000'

    print('🔍 检查项目编辑配置')
    print('=' * 50)

    # 1. 获取项目文件
    print('📁 项目文件:')
    response = requests.get(f'{base_url}/api/projects/{project_id}/files')
    if response.status_code == 200:
        files = response.json()
        video_files = [f for f in files if f['file_type'] == 'video']
        print(f'   视频文件数量: {len(video_files)}')
        for i, f in enumerate(video_files):
            size_mb = f['file_size'] / (1024 * 1024)
            print(f'   {i+1}. {f["filename"]} ({size_mb:.2f} MB)')
    else:
        print(f'   ❌ 无法获取文件: {response.status_code}')

    # 2. 获取最近的任务
    print(f'\n📋 最近的编辑任务:')
    response = requests.get(f'{base_url}/api/projects/{project_id}/tasks')
    if response.status_code == 200:
        tasks = response.json()
        print(f'   任务数量: {len(tasks)}')
        
        for i, task in enumerate(tasks[:5]):  # 显示最近5个任务
            print(f'\n   任务 {i+1}:')
            print(f'   - ID: {task["id"][:8]}...')
            print(f'   - 状态: {task["status"]}')
            print(f'   - 进度: {task["progress"]}%')
            print(f'   - 创建时间: {task.get("created_at", "N/A")}')
            
            if 'config' in task and task['config']:
                try:
                    config = json.loads(task['config']) if isinstance(task['config'], str) else task['config']
                    
                    if 'middle_video_filenames' in config:
                        middle_videos = config['middle_video_filenames']
                        print(f'   - 片中视频: {middle_videos} (数量: {len(middle_videos)})')
                    
                    if 'output_size' in config:
                        print(f'   - 输出尺寸: {config["output_size"]}')
                        
                    # 检查其他配置
                    other_configs = []
                    if config.get('intro_filename'):
                        other_configs.append(f"片头: {config['intro_filename']}")
                    if config.get('outro_filename'):
                        other_configs.append(f"片尾: {config['outro_filename']}")
                    if config.get('mute_original_audio'):
                        other_configs.append("静音原音频")
                    if config.get('subtitle_filename'):
                        other_configs.append(f"字幕: {config['subtitle_filename']}")
                    
                    if other_configs:
                        print(f'   - 其他配置: {", ".join(other_configs)}')
                        
                except Exception as e:
                    print(f'   - 配置解析错误: {e}')
            
            if 'output_path' in task and task['output_path']:
                print(f'   - 输出文件: {task["output_path"]}')
    else:
        print(f'   ❌ 无法获取任务: {response.status_code}')

    # 3. 检查最新输出文件
    print(f'\n📊 检查最新输出文件:')
    import os
    from moviepy.editor import VideoFileClip
    
    output_dir = f'backend/storage/outputs/{project_id}'
    if os.path.exists(output_dir):
        output_files = [f for f in os.listdir(output_dir) if f.endswith('.mp4')]
        if output_files:
            latest_file = max(output_files, key=lambda f: os.path.getmtime(os.path.join(output_dir, f)))
            file_path = os.path.join(output_dir, latest_file)
            
            try:
                clip = VideoFileClip(file_path)
                duration = clip.duration
                size = clip.size
                fps = clip.fps
                clip.close()
                
                file_size = os.path.getsize(file_path) / (1024 * 1024)
                
                print(f'   最新文件: {latest_file}')
                print(f'   文件大小: {file_size:.2f} MB')
                print(f'   视频尺寸: {size[0]}x{size[1]}')
                print(f'   时长: {duration:.2f}s')
                print(f'   帧率: {fps:.2f} fps')
                
                # 分析时长是否合理
                if duration < 5:
                    print(f'   ⚠️ 警告: 视频时长很短 ({duration:.2f}s)，可能只包含一个视频片段')
                elif duration > 60:
                    print(f'   ✅ 视频时长正常 ({duration:.2f}s)，可能包含多个视频片段')
                else:
                    print(f'   📊 视频时长: {duration:.2f}s')
                    
            except Exception as e:
                print(f'   ❌ 无法分析视频文件: {e}')
        else:
            print(f'   ❌ 输出目录中没有找到视频文件')
    else:
        print(f'   ❌ 输出目录不存在: {output_dir}')

if __name__ == "__main__":
    check_project_config()
