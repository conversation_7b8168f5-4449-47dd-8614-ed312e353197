#!/usr/bin/env python3
"""Test editing API with real project"""

import requests

def test_editing_api():
    """Test editing API with real project ID"""
    print("🔍 Testing editing API with real project...")
    
    # First get the latest project
    projects_response = requests.get('http://localhost:8000/api/projects/')
    
    if projects_response.status_code != 200:
        print(f"❌ Failed to get projects: {projects_response.status_code}")
        return
    
    projects = projects_response.json()
    if not projects:
        print("❌ No projects found")
        return
    
    # Use the first project
    project = projects[0]
    project_id = project['id']
    print(f"📋 Using project: {project['name']} (ID: {project_id})")
    
    # Test editing endpoint
    editing_data = {
        'project_id': project_id,
        'main_video_path': 'test/path/video.mp4',
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    try:
        response = requests.post('http://localhost:8000/api/editing/start', json=editing_data)
        print(f"Editing API status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Editing started successfully!")
            print(f"📋 Task ID: {result.get('task_id')}")
        elif response.status_code == 404:
            print("❌ Editing API returns 404 - endpoint not found or project not found")
        elif response.status_code == 422:
            print("⚠️ Editing API returns 422 - validation error")
            print(f"Error details: {response.text}")
        else:
            print(f"📋 Editing API response: {response.text}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")

if __name__ == "__main__":
    test_editing_api()
