#!/usr/bin/env python3
"""Debug project existence"""

import requests

def test_project_debug():
    """Debug project existence"""
    print("🔍 Debugging project existence...")
    
    # Get all projects
    projects_response = requests.get('http://localhost:8000/api/projects/')
    
    if projects_response.status_code != 200:
        print(f"❌ Failed to get projects: {projects_response.status_code}")
        return
    
    projects = projects_response.json()
    print(f"📋 Found {len(projects)} projects:")
    
    for project in projects:
        print(f"  - ID: {project['id']}")
        print(f"    Name: {project['name']}")
        print(f"    Status: {project['status']}")
        
        # Test individual project retrieval
        project_response = requests.get(f'http://localhost:8000/api/projects/{project["id"]}')
        print(f"    Individual retrieval: {project_response.status_code}")
        
        if project_response.status_code == 200:
            individual_project = project_response.json()
            print(f"    Retrieved name: {individual_project['name']}")
        else:
            print(f"    Error: {project_response.text}")
        
        print()

if __name__ == "__main__":
    test_project_debug()
