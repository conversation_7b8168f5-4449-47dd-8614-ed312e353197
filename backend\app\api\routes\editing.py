from fastapi import APIRouter, Body, HTTPException, Depends
from app.tasks.editing_tasks import process_video_task
from app.models.job import create_job, JobCreate
from app.api.dependencies import validate_editing_config, validate_project_exists
import uuid

router = APIRouter()

@router.post("/start")
async def start_editing(
    config: dict = Depends(validate_editing_config)
):
    """開始視頻編輯任務"""
    try:
        # 驗證項目存在
        project_id = config["project_id"]
        project = validate_project_exists(project_id)

        # 創建任務ID
        job_id = str(uuid.uuid4())

        # 創建任務記錄
        create_job(project_id, config, job_id)

        # 啟動後台任務
        process_video_task.delay(job_id, config["main_video_path"], config)

        return {
            "job_id": job_id,
            "project_id": project_id,
            "status": "pending",
            "message": "Editing task started successfully",
            "config": config
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start editing task: {str(e)}")

@router.post("/cancel/{job_id}")
async def cancel_editing(job_id: str):
    """取消編輯任務"""
    try:
        from app.models.job import update_job_status, get_job

        # 檢查任務是否存在
        job = get_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # 只能取消待處理或進行中的任務
        if job['status'] not in ['pending', 'processing']:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot cancel job with status: {job['status']}"
            )

        # 更新任務狀態為已取消
        success = update_job_status(job_id, 'cancelled', error="Task cancelled by user")

        if not success:
            raise HTTPException(status_code=500, detail="Failed to cancel task")

        return {
            "job_id": job_id,
            "status": "cancelled",
            "message": "Task cancelled successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")

@router.post("/retry/{job_id}")
async def retry_editing(job_id: str):
    """重試失敗的編輯任務"""
    try:
        from app.models.job import get_job, update_job_status

        # 檢查任務是否存在
        job = get_job(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # 只能重試失敗的任務
        if job['status'] != 'failed':
            raise HTTPException(
                status_code=400,
                detail=f"Cannot retry job with status: {job['status']}"
            )

        # 重置任務狀態
        update_job_status(job_id, 'pending', progress=0, error=None)

        # 重新啟動任務
        config = job.get('config', {})
        main_video_path = config.get('main_video_path')

        if not main_video_path:
            raise HTTPException(status_code=400, detail="Invalid job configuration")

        process_video_task.delay(job_id, main_video_path, config)

        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Task restarted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retry task: {str(e)}")