from fastapi import APIRouter, Body, HTTPException
from app.tasks.editing_tasks import process_video_task
from app.models.job import create_job
import uuid

router = APIRouter()

@router.post("/start")
async def start_editing(
    config: dict = Body(...)
):
    """開始視頻編輯任務"""
    try:
        # 驗證配置
        if not config.get("project_id") or not config.get("main_video_path"):
            raise HTTPException(status_code=400, detail="Missing required parameters")
        
        # 創建任務ID
        job_id = str(uuid.uuid4())
        
        # 創建任務記錄
        create_job(job_id, config["project_id"], "pending")
        
        # 啟動後台任務
        process_video_task(job_id, config["main_video_path"], config)
        
        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Editing task started"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))