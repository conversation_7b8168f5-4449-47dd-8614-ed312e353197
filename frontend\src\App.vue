<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 頂部導航 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><VideoCamera /></el-icon>
            <span>自動視頻剪輯平台</span>
          </div>
          <el-menu
            mode="horizontal"
            :default-active="$route.path"
            router
            class="header-menu"
          >
            <el-menu-item index="/">首頁</el-menu-item>
            <el-menu-item index="/projects">項目管理</el-menu-item>
            <el-menu-item index="/upload">文件上傳</el-menu-item>
            <el-menu-item index="/tasks">任務監控</el-menu-item>
          </el-menu>
        </div>
      </el-header>

      <!-- 主內容區域 -->
      <el-main class="app-main">
        <router-view />
      </el-main>

      <!-- 底部 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <span>© 2024 自動視頻剪輯平台. All rights reserved.</span>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import { VideoCamera } from '@element-plus/icons-vue'

export default {
  name: 'App',
  components: {
    VideoCamera
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}

.header-menu {
  border-bottom: none;
}

.app-main {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  padding: 20px;
}

.app-footer {
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-content {
  color: #909399;
  font-size: 14px;
}
</style>

<style>
/* 全局樣式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  width: 100%;
  height: 100%;
}
</style>
