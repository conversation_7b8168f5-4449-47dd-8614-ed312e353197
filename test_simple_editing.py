#!/usr/bin/env python3
"""
測試簡單的視頻編輯功能 - 只處理視頻，不包含音頻和字幕
"""

import requests
import json
import time

def test_simple_editing():
    """測試簡單的視頻編輯"""
    base_url = 'http://localhost:8000'
    project_id = 'e26a251a-0e00-4db7-a3fc-24b0341e2277'
    
    print("🎬 測試簡單視頻編輯...")
    print("=" * 50)
    
    # 獲取項目文件
    response = requests.get(f'{base_url}/api/projects/{project_id}/files')
    if response.status_code != 200:
        print(f"❌ 無法獲取文件列表: {response.status_code}")
        return
    
    files = response.json()
    video_files = [f for f in files if f['file_type'] == 'video']
    
    if not video_files:
        print("❌ 沒有視頻文件")
        return
    
    main_video = video_files[0]
    print(f"📹 主視頻: {main_video['filename']}")
    
    # 簡單的編輯配置 - 只處理視頻
    editing_config = {
        'project_id': project_id,
        'main_video_filename': main_video['filename'],
        'output_size': '720x720',  # 方形格式，處理更快
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0,
        'intro_text': '',
        'outro_text': '',
        'background_music': ''
        # 不包含音頻和字幕文件
    }
    
    print("📝 編輯配置:")
    print(json.dumps(editing_config, indent=2, ensure_ascii=False))
    
    # 發送編輯請求
    print("\n🚀 開始編輯...")
    response = requests.post(f'{base_url}/api/editing/start', json=editing_config)
    
    if response.status_code == 200:
        result = response.json()
        job_id = result.get('job_id')
        print(f"✅ 編輯任務已啟動，任務ID: {job_id}")
        
        # 監控進度
        monitor_progress(base_url, job_id)
        
    else:
        print(f"❌ 編輯啟動失敗:")
        print(f"   狀態碼: {response.status_code}")
        print(f"   錯誤信息: {response.text}")

def monitor_progress(base_url, job_id):
    """監控編輯進度"""
    print(f"\n📊 監控任務進度: {job_id}")
    
    for i in range(1, 15):  # 最多監控15次
        try:
            response = requests.get(f'{base_url}/api/progress/{job_id}')
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                progress = data.get('progress', 0)
                
                print(f"第{i}次檢查 - 狀態: {status}, 進度: {progress}%")
                
                if status == 'completed':
                    output_path = data.get('output_path', 'N/A')
                    print(f"🎉 編輯完成! 輸出文件: {output_path}")
                    
                    # 檢查輸出文件
                    check_output_file(output_path)
                    break
                    
                elif status == 'failed':
                    error = data.get('error', 'Unknown error')
                    print(f"💥 編輯失敗: {error}")
                    break
                    
                elif status == 'cancelled':
                    print(f"⏹️ 編輯已取消")
                    break
            else:
                print(f"⚠️ 無法獲取進度: {response.status_code}")
        
        except Exception as e:
            print(f"❌ 檢查進度時出錯: {e}")
        
        if i < 14:
            print("   等待3秒...")
            time.sleep(3)
    
    print("📊 進度監控結束")

def check_output_file(output_path):
    """檢查輸出文件"""
    import os
    
    if not output_path or output_path == 'N/A':
        print("⚠️ 沒有輸出文件路徑")
        return
    
    # 嘗試不同的路徑組合
    possible_paths = [
        output_path,
        os.path.join('backend', output_path),
        output_path.replace('\\', '/'),
        os.path.join('backend', output_path.replace('\\', '/'))
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            print(f"📁 輸出文件確認存在: {path}")
            print(f"   文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            return
    
    print(f"❌ 輸出文件不存在: {output_path}")

if __name__ == "__main__":
    test_simple_editing()
