#!/usr/bin/env python3
"""
測試開始編輯功能 - 全新重啟後測試
"""

import requests
import json
import time

def test_editing_functionality():
    base_url = 'http://localhost:8000'
    
    print("🔍 檢查現有項目和文件...")
    
    # 1. 獲取項目列表
    response = requests.get(f'{base_url}/api/projects/')
    if response.status_code == 200:
        projects = response.json()
        print(f"找到 {len(projects)} 個項目:")
        for project in projects:
            print(f"  - {project['name']} (ID: {project['id']})")
            
            # 檢查項目文件
            files_response = requests.get(f"{base_url}/api/projects/{project['id']}/files")
            if files_response.status_code == 200:
                files = files_response.json()
                print(f"    文件數量: {len(files)}")
                for file in files[:3]:  # 只顯示前3個文件
                    print(f"    - {file['filename']}")
                if len(files) > 3:
                    print(f"    ... 還有 {len(files) - 3} 個文件")
            
            # 如果有文件，測試編輯功能
            if files:
                print(f"\n🎬 測試項目 '{project['name']}' 的編輯功能...")
                test_project_editing(base_url, project['id'], files[0]['filename'])
                break
    else:
        print(f"❌ 無法獲取項目列表: {response.status_code}")

def test_project_editing(base_url, project_id, main_video_filename):
    """測試項目編輯功能"""
    
    # 編輯配置
    config = {
        'project_id': project_id,
        'main_video_filename': main_video_filename,
        'output_size': '720x720',  # 測試方形尺寸
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print(f"📝 編輯配置:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    # 開始編輯
    print("\n🚀 開始編輯...")
    response = requests.post(f'{base_url}/api/editing/start', json=config)
    
    if response.status_code == 200:
        result = response.json()
        job_id = result.get('job_id')
        print(f"✅ 編輯任務已啟動，任務ID: {job_id}")
        
        # 監控進度
        monitor_progress(base_url, job_id)
        
    else:
        print(f"❌ 編輯啟動失敗:")
        print(f"   狀態碼: {response.status_code}")
        print(f"   錯誤信息: {response.text}")

def monitor_progress(base_url, job_id):
    """監控編輯進度"""
    print(f"\n📊 監控任務進度: {job_id}")
    
    for i in range(1, 11):  # 最多檢查10次
        try:
            response = requests.get(f'{base_url}/api/progress/{job_id}')
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                progress = data.get('progress', 0)
                
                print(f"第{i}次檢查 - 狀態: {status}, 進度: {progress}%")
                
                if status == 'completed':
                    output_path = data.get('output_path', 'N/A')
                    print(f"🎉 編輯完成! 輸出文件: {output_path}")
                    break
                elif status == 'failed':
                    error = data.get('error', 'Unknown error')
                    print(f"💥 編輯失敗: {error}")
                    break
                elif status == 'cancelled':
                    print(f"⏹️ 編輯已取消")
                    break
            else:
                print(f"⚠️ 無法獲取進度: {response.status_code} - {response.text}")
        
        except Exception as e:
            print(f"❌ 檢查進度時出錯: {e}")
        
        if i < 10:
            print("   等待3秒...")
            time.sleep(3)
    
    print("📊 進度監控結束")

if __name__ == "__main__":
    test_editing_functionality()
