from fastapi import APIRouter, UploadFile, File, Form, HTTPException
import os
import uuid
import shutil

router = APIRouter()

@router.post("/")
async def upload_files(
    files: list[UploadFile] = File(...),
    project_id: str = Form(None)
):
    """上傳多個文件，支持n8n集成"""
    if not project_id:
        project_id = str(uuid.uuid4())
    
    upload_dir = f"storage/uploads/{project_id}"
    os.makedirs(upload_dir, exist_ok=True)
    
    file_info = []
    for file in files:
        file_path = os.path.join(upload_dir, file.filename)
        
        # 保存上傳的文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        file_info.append({
            "filename": file.filename,
            "path": file_path,
            "content_type": file.content_type
        })
    
    return {
        "project_id": project_id,
        "files": file_info
    }