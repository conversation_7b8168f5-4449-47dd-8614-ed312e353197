from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from typing import List
import os
import uuid
import shutil
from app.core.config import settings
from app.models.project import create_project, get_project, add_project_file, ProjectCreate
from app.api.dependencies import validate_file_upload

router = APIRouter()

@router.post("/")
async def upload_files(
    files: List[UploadFile] = Depends(validate_file_upload),
    project_id: str = Form(None),
    project_name: str = Form(None)
):
    """上傳多個文件，支持n8n集成"""
    try:
        # 如果沒有提供項目ID，創建新項目
        if not project_id:
            project_id = str(uuid.uuid4())
            # 創建項目記錄
            project_data = ProjectCreate(
                name=project_name or f"Project {project_id[:8]}",
                description="Auto-created project from file upload"
            )
            create_project(project_data)
        else:
            # 驗證項目是否存在，如果不存在則創建
            existing_project = get_project(project_id)
            if not existing_project:
                project_data = ProjectCreate(
                    name=project_name or f"Project {project_id[:8]}",
                    description="Auto-created project from file upload"
                )
                create_project(project_data)

        upload_dir = os.path.join(settings.upload_dir, project_id)
        os.makedirs(upload_dir, exist_ok=True)

        file_info = []
        for file in files:
            file_path = os.path.join(upload_dir, file.filename)

            # 保存上傳的文件
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # 獲取文件大小
            file_size = os.path.getsize(file_path)

            # 確定文件類型
            file_ext = os.path.splitext(file.filename)[1].lower()
            if file_ext in ['.mov', '.mp4', '.avi', '.mkv', '.webm']:
                file_type = 'video'
            elif file_ext in ['.srt', '.ass', '.ssa', '.vtt']:
                file_type = 'subtitle'
            elif file_ext in ['.wav', '.mp3', '.aac', '.flac', '.ogg', '.m4a']:
                file_type = 'audio'
            else:
                file_type = 'other'

            # 添加文件記錄到數據庫
            file_record = {
                'filename': file.filename,
                'file_path': file_path,
                'file_type': file_type,
                'file_size': file_size,
                'content_type': file.content_type
            }

            file_id = add_project_file(project_id, file_record)

            file_info.append({
                "id": file_id,
                "filename": file.filename,
                "path": file_path,
                "file_type": file_type,
                "file_size": file_size,
                "content_type": file.content_type
            })

        return {
            "project_id": project_id,
            "files": file_info,
            "message": f"Successfully uploaded {len(files)} files"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")