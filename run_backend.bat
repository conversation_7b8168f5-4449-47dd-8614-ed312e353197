@echo off
chcp 65001 >nul
title 自動視頻剪輯平台 - 後端服務

echo ========================================
echo 🔧 自動視頻剪輯平台 - 後端服務
echo ========================================
echo.

:: 檢查虛擬環境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虛擬環境不存在，請先運行 setup_windows.bat
    pause
    exit /b 1
)

:: 激活虛擬環境
echo 🚀 激活虛擬環境...
call venv\Scripts\activate.bat

:: 檢查後端目錄
if not exist "backend" (
    echo ❌ 後端目錄不存在
    pause
    exit /b 1
)

:: 檢查配置文件
if not exist "backend\.env" (
    echo ⚠️  配置文件不存在，使用默認配置
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env"
        echo ✅ 已創建配置文件
    )
)

:: 檢查數據庫
if not exist "storage\app.db" (
    echo 🗄️  初始化數據庫...
    cd backend
    python -c "from app.core.database import init_db; init_db(); print('數據庫初始化完成')"
    cd ..
)

echo 🚀 啟動後端服務...
echo.
echo 📋 服務信息：
echo - API 地址: http://localhost:8000
echo - API 文檔: http://localhost:8000/docs
echo - 健康檢查: http://localhost:8000/health
echo.
echo 💡 按 Ctrl+C 停止服務
echo.

cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
