#!/usr/bin/env python3
"""
测试修复后的视频尺寸调整功能
"""
import requests
import json
import time
import os
from moviepy.editor import VideoFileClip

def check_video_info(video_path):
    """检查视频文件信息"""
    try:
        if os.path.exists(video_path):
            clip = VideoFileClip(video_path)
            info = {
                "path": video_path,
                "size": clip.size,
                "duration": clip.duration,
                "fps": clip.fps
            }
            clip.close()
            return info
        else:
            return {"error": f"File not found: {video_path}"}
    except Exception as e:
        return {"error": str(e)}

def test_size_fix():
    """测试修复后的尺寸调整功能"""
    base_url = "http://localhost:8000"
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    print("🔧 测试修复后的视频尺寸调整功能")
    print("=" * 50)
    
    # 1. 检查项目文件
    print("📁 检查项目文件...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/files")
    if response.status_code != 200:
        print(f"❌ 无法获取项目文件: {response.status_code}")
        return
    
    files = response.json()
    video_files = [f for f in files if f['file_type'] == 'video']
    
    # 选择一个较小的视频进行测试
    selected_video = video_files[1]['filename']  # 1.MOV (6.53 MB)
    
    print(f"📹 选择测试视频: {selected_video}")
    
    # 检查原始视频信息
    video_path = f"backend/storage/uploads/{project_id}/{selected_video}"
    original_info = check_video_info(video_path)
    if "error" not in original_info:
        print(f"📊 原始尺寸: {original_info['size']} ({original_info['size'][0]}x{original_info['size'][1]})")
    
    # 2. 测试1080x1920尺寸设置
    print(f"\n🧪 测试1080x1920尺寸设置...")
    
    config = {
        "project_id": project_id,
        "middle_video_filenames": [selected_video],
        "output_size": "1080x1920",  # 目标尺寸
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"📝 编辑配置:")
    print(f"  - 视频: {selected_video}")
    print(f"  - 目标尺寸: {config['output_size']}")
    
    # 3. 启动编辑任务
    print("\n🚀 启动编辑任务...")
    
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    if response.status_code != 200:
        print(f"❌ 启动编辑失败: {response.status_code}")
        print(f"错误: {response.text}")
        return
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ 任务已启动，ID: {job_id}")
    
    # 4. 监控进度
    print("\n📊 监控编辑进度...")
    
    start_time = time.time()
    for i in range(1, 31):  # 最多检查30次
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            print(f"⚠️ 无法获取进度: {response.status_code}")
            time.sleep(3)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        elapsed = time.time() - start_time
        
        print(f"第{i:2d}次检查 - 状态: {status:10s} 进度: {progress:3d}% 耗时: {elapsed:5.1f}s")
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"\n✅ 编辑完成!")
            print(f"📁 输出文件: {output_path}")
            
            # 检查输出视频的实际尺寸
            full_output_path = f"backend/{output_path}"
            output_info = check_video_info(full_output_path)
            
            if "error" not in output_info:
                actual_size = output_info['size']
                expected_size = (1080, 1920)
                
                print(f"\n📊 尺寸验证:")
                print(f"  期望尺寸: {expected_size[0]}x{expected_size[1]}")
                print(f"  实际尺寸: {actual_size[0]}x{actual_size[1]}")
                
                width_diff = actual_size[0] - expected_size[0]
                height_diff = actual_size[1] - expected_size[1]

                if actual_size == expected_size:
                    print(f"  ✅ 尺寸正确! 修复成功!")
                    print(f"  🎯 完美匹配: 差异 ({width_diff}, {height_diff})")
                    return True
                else:
                    print(f"  ❌ 尺寸仍不匹配!")
                    print(f"  🔍 问题分析:")
                    print(f"    - 宽度差异: {width_diff}")
                    print(f"    - 高度差异: {height_diff}")
                    return False
                
            else:
                print(f"⚠️ 无法检查输出视频: {output_info['error']}")
                return False
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"\n❌ 编辑失败: {error}")
            return False
        
        if i < 30:
            time.sleep(3)
    
    print(f"\n⏰ 编辑超时")
    return False

if __name__ == "__main__":
    success = test_size_fix()
    
    if success:
        print("\n🎉 尺寸修复测试成功!")
        print("✅ 1080x1920尺寸调整正常工作")
    else:
        print("\n💥 尺寸修复测试失败")
        print("❌ 需要进一步调试")
