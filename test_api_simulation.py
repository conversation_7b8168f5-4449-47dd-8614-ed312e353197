#!/usr/bin/env python3
"""Simulate API call"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_api_simulation():
    """Simulate the API call logic"""
    print("🔍 Simulating API call logic...")
    
    try:
        # Import everything needed
        from app.tasks.editing_tasks import process_video_task
        from app.models.job import create_job
        import uuid
        
        # Create test video file
        test_video_path = os.path.join(os.getcwd(), "storage", "test_video.mp4")
        os.makedirs(os.path.dirname(test_video_path), exist_ok=True)
        
        # Create a dummy video file
        with open(test_video_path, 'wb') as f:
            f.write(b'\x00' * 1024)  # 1KB dummy file
        
        print(f"📋 Created test file: {test_video_path}")
        
        # Simulate API logic
        project_id = "b9da6931-653e-4a2c-a575-a26dadeb6c08"
        config = {
            "project_id": project_id,
            "main_video_path": test_video_path,
            "subtitle_effect": "none",
            "background_volume": 0.3,
            "audio_volume": 1.0
        }
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        print(f"📋 Generated job ID: {job_id}")
        
        # Create job record
        print("📋 Creating job record...")
        try:
            create_job(project_id, config, job_id)
            print("✅ Job record created successfully")
        except Exception as e:
            print(f"❌ Job record creation failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Start background task
        print("📋 Starting background task...")
        try:
            result = process_video_task.schedule(args=(job_id, config["main_video_path"], config), delay=0)
            print(f"✅ Background task started: {result}")
            print(f"📋 Result type: {type(result)}")
            print(f"📋 Result attributes: {[attr for attr in dir(result) if not attr.startswith('_')]}")
        except Exception as e:
            print(f"❌ Background task failed: {e}")
            print(f"📋 Error type: {type(e)}")
            import traceback
            traceback.print_exc()
        
        # Clean up
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
            print(f"🧹 Cleaned up test file: {test_video_path}")
            
    except Exception as e:
        print(f"❌ Simulation error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_simulation()
