#!/usr/bin/env python3
"""
快速验证视频尺寸修复
"""
import os
from moviepy.editor import VideoFileClip

def quick_test():
    """快速检查最新生成的视频尺寸"""
    output_path = "backend/storage/outputs/e26a251a-0e00-4db7-a3fc-24b0341e2277/final_video.mp4"
    
    print("🔍 快速验证视频尺寸修复")
    print("=" * 40)
    
    if os.path.exists(output_path):
        try:
            clip = VideoFileClip(output_path)
            actual_size = clip.size
            expected_size = (1080, 1920)
            
            print(f"📁 检查文件: {output_path}")
            print(f"📊 期望尺寸: {expected_size[0]}x{expected_size[1]}")
            print(f"📊 实际尺寸: {actual_size[0]}x{actual_size[1]}")
            
            width_diff = actual_size[0] - expected_size[0]
            height_diff = actual_size[1] - expected_size[1]

            print(f"🔍 差异分析: ({width_diff}, {height_diff})")

            if width_diff == 0 and height_diff == 0:
                print(f"✅ 尺寸修复成功!")
                print(f"🎉 1080x1920 调整正常工作")
                return True
            else:
                print(f"❌ 尺寸仍有问题")
                print(f"差异: ({width_diff}, {height_diff})")
                return False
                
            clip.close()
            
        except Exception as e:
            print(f"❌ 检查视频时出错: {e}")
            return False
    else:
        print(f"❌ 输出文件不存在: {output_path}")
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎯 结论: 视频尺寸修复成功!")
    else:
        print("\n💥 结论: 视频尺寸修复失败")
