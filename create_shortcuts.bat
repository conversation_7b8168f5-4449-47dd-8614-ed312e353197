@echo off
chcp 65001 >nul
echo ========================================
echo 🔗 創建桌面快捷方式
echo ========================================
echo.

:: 獲取當前目錄
set "CURRENT_DIR=%CD%"

:: 獲取桌面路徑
for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop 2^>nul') do set "DESKTOP=%%i %%j"

echo 📁 當前目錄: %CURRENT_DIR%
echo 🖥️  桌面路徑: %DESKTOP%
echo.

:: 創建 VBS 腳本來生成快捷方式
echo 🔧 創建快捷方式生成腳本...

:: 環境設置快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\自動視頻剪輯平台 - 環境設置.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\setup_windows.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "自動視頻剪輯平台 - 環境設置" >> CreateShortcut.vbs
echo oLink.IconLocation = "shell32.dll,21" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

:: 系統啟動快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut2.vbs
echo sLinkFile = "%DESKTOP%\自動視頻剪輯平台 - 啟動系統.lnk" >> CreateShortcut2.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut2.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\start_windows.bat" >> CreateShortcut2.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut2.vbs
echo oLink.Description = "自動視頻剪輯平台 - 啟動系統" >> CreateShortcut2.vbs
echo oLink.IconLocation = "shell32.dll,25" >> CreateShortcut2.vbs
echo oLink.Save >> CreateShortcut2.vbs

:: 系統測試快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut3.vbs
echo sLinkFile = "%DESKTOP%\自動視頻剪輯平台 - 系統測試.lnk" >> CreateShortcut3.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut3.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\test_windows.bat" >> CreateShortcut3.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut3.vbs
echo oLink.Description = "自動視頻剪輯平台 - 系統測試" >> CreateShortcut3.vbs
echo oLink.IconLocation = "shell32.dll,23" >> CreateShortcut3.vbs
echo oLink.Save >> CreateShortcut3.vbs

:: 停止服務快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut4.vbs
echo sLinkFile = "%DESKTOP%\自動視頻剪輯平台 - 停止服務.lnk" >> CreateShortcut4.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut4.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\stop_windows.bat" >> CreateShortcut4.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut4.vbs
echo oLink.Description = "自動視頻剪輯平台 - 停止服務" >> CreateShortcut4.vbs
echo oLink.IconLocation = "shell32.dll,28" >> CreateShortcut4.vbs
echo oLink.Save >> CreateShortcut4.vbs

:: 執行 VBS 腳本
echo 🔗 正在創建快捷方式...
cscript //nologo CreateShortcut.vbs
if not errorlevel 1 echo ✅ 環境設置快捷方式已創建

cscript //nologo CreateShortcut2.vbs
if not errorlevel 1 echo ✅ 系統啟動快捷方式已創建

cscript //nologo CreateShortcut3.vbs
if not errorlevel 1 echo ✅ 系統測試快捷方式已創建

cscript //nologo CreateShortcut4.vbs
if not errorlevel 1 echo ✅ 停止服務快捷方式已創建

:: 清理臨時文件
del CreateShortcut.vbs >nul 2>&1
del CreateShortcut2.vbs >nul 2>&1
del CreateShortcut3.vbs >nul 2>&1
del CreateShortcut4.vbs >nul 2>&1

echo.
echo 🎉 桌面快捷方式創建完成！
echo.
echo 📋 已創建的快捷方式：
echo - 自動視頻剪輯平台 - 環境設置.lnk
echo - 自動視頻剪輯平台 - 啟動系統.lnk
echo - 自動視頻剪輯平台 - 系統測試.lnk
echo - 自動視頻剪輯平台 - 停止服務.lnk
echo.
echo 💡 使用說明：
echo 1. 首次使用請先雙擊「環境設置」快捷方式
echo 2. 環境設置完成後，雙擊「啟動系統」快捷方式
echo 3. 可以使用「系統測試」快捷方式檢查系統狀態
echo 4. 使用「停止服務」快捷方式安全停止所有服務
echo.
pause
