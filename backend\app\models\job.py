import uuid
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from app.core.database import db

class JobConfig(BaseModel):
    """任務配置模型"""
    project_id: str
    main_video_path: str
    subtitle_path: Optional[str] = None
    audio_path: Optional[str] = None
    subtitle_effect: Optional[str] = None
    intro_path: Optional[str] = None
    outro_path: Optional[str] = None
    video_codec: Optional[str] = None
    audio_codec: Optional[str] = None
    video_bitrate: Optional[str] = None
    audio_bitrate: Optional[str] = None

class Job(BaseModel):
    """任務模型"""
    id: str
    project_id: str
    status: str = "pending"  # pending, processing, completed, failed
    progress: int = 0
    config: Optional[Dict[str, Any]] = None
    output_path: Optional[str] = None
    error: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class JobCreate(BaseModel):
    """創建任務的請求模型"""
    project_id: str
    config: Dict[str, Any]

class JobResponse(BaseModel):
    """任務響應模型"""
    id: str
    project_id: str
    status: str
    progress: int
    output_path: Optional[str] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime

def create_job(project_id: str, config: Dict[str, Any], job_id: str = None) -> str:
    """創建新任務"""
    if not job_id:
        job_id = str(uuid.uuid4())
    
    config_json = json.dumps(config) if config else None
    
    query = """
    INSERT INTO jobs (id, project_id, status, progress, config, created_at, updated_at)
    VALUES (?, ?, 'pending', 0, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    """
    
    db.execute_insert(query, (job_id, project_id, config_json))
    return job_id

def get_job(job_id: str) -> Optional[Dict[str, Any]]:
    """根據ID獲取任務"""
    query = "SELECT * FROM jobs WHERE id = ?"
    results = db.execute_query(query, (job_id,))
    
    if not results:
        return None
    
    job = results[0]
    
    # 解析配置JSON
    if job.get('config'):
        try:
            job['config'] = json.loads(job['config'])
        except json.JSONDecodeError:
            job['config'] = None
    
    return job

def get_jobs_by_project(project_id: str) -> List[Dict[str, Any]]:
    """獲取項目的所有任務"""
    query = "SELECT * FROM jobs WHERE project_id = ? ORDER BY created_at DESC"
    jobs = db.execute_query(query, (project_id,))
    
    # 解析每個任務的配置JSON
    for job in jobs:
        if job.get('config'):
            try:
                job['config'] = json.loads(job['config'])
            except json.JSONDecodeError:
                job['config'] = None
    
    return jobs

def get_all_jobs(status: Optional[str] = None) -> List[Dict[str, Any]]:
    """獲取所有任務，可選擇按狀態過濾"""
    if status:
        query = "SELECT * FROM jobs WHERE status = ? ORDER BY created_at DESC"
        jobs = db.execute_query(query, (status,))
    else:
        query = "SELECT * FROM jobs ORDER BY created_at DESC"
        jobs = db.execute_query(query)
    
    # 解析每個任務的配置JSON
    for job in jobs:
        if job.get('config'):
            try:
                job['config'] = json.loads(job['config'])
            except json.JSONDecodeError:
                job['config'] = None
    
    return jobs

def update_job_status(job_id: str, status: str, progress: int = None, 
                     output_path: str = None, error: str = None) -> bool:
    """更新任務狀態"""
    # 構建動態更新查詢
    set_clauses = ["status = ?", "updated_at = CURRENT_TIMESTAMP"]
    params = [status]
    
    if progress is not None:
        set_clauses.append("progress = ?")
        params.append(progress)
    
    if output_path is not None:
        set_clauses.append("output_path = ?")
        params.append(output_path)
    
    if error is not None:
        set_clauses.append("error = ?")
        params.append(error)
    
    params.append(job_id)
    
    query = f"UPDATE jobs SET {', '.join(set_clauses)} WHERE id = ?"
    
    rows_affected = db.execute_update(query, tuple(params))
    return rows_affected > 0

def update_job_progress(job_id: str, progress: int) -> bool:
    """更新任務進度"""
    query = "UPDATE jobs SET progress = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    rows_affected = db.execute_update(query, (progress, job_id))
    return rows_affected > 0

def delete_job(job_id: str) -> bool:
    """刪除任務"""
    query = "DELETE FROM jobs WHERE id = ?"
    rows_affected = db.execute_update(query, (job_id,))
    return rows_affected > 0

def get_pending_jobs() -> List[Dict[str, Any]]:
    """獲取所有待處理的任務"""
    return get_all_jobs(status="pending")

def get_processing_jobs() -> List[Dict[str, Any]]:
    """獲取所有正在處理的任務"""
    return get_all_jobs(status="processing")

def get_completed_jobs() -> List[Dict[str, Any]]:
    """獲取所有已完成的任務"""
    return get_all_jobs(status="completed")

def get_failed_jobs() -> List[Dict[str, Any]]:
    """獲取所有失敗的任務"""
    return get_all_jobs(status="failed")
