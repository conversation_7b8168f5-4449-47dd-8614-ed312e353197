#!/usr/bin/env python3
"""Test editing API directly"""

import requests

def test_editing_direct():
    """Test editing API directly with known project ID"""
    print("🔍 Testing editing API directly...")
    
    # Use a known project ID
    project_id = "b9da6931-653e-4a2c-a575-a26dadeb6c08"
    
    # Test editing endpoint
    editing_data = {
        'project_id': project_id,
        'main_video_path': 'test/path/video.mp4',
        'subtitle_effect': 'none',
        'background_volume': 0.3,
        'audio_volume': 1.0
    }
    
    print(f"📋 Testing with project ID: {project_id}")
    print(f"📋 Editing data: {editing_data}")
    
    try:
        response = requests.post('http://localhost:8000/api/editing/start', json=editing_data)
        print(f"Editing API status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Editing started successfully!")
            print(f"📋 Task ID: {result.get('task_id')}")
        elif response.status_code == 404:
            print("❌ Editing API returns 404 - project not found")
        elif response.status_code == 422:
            print("⚠️ Editing API returns 422 - validation error")
        elif response.status_code == 400:
            print("⚠️ Editing API returns 400 - bad request")
        else:
            print(f"📋 Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Editing API error: {e}")

if __name__ == "__main__":
    test_editing_direct()
