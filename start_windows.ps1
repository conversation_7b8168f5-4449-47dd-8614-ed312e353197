# 自動視頻剪輯平台 - PowerShell 啟動腳本
# 需要 PowerShell 5.0+ 或 PowerShell Core

param(
    [switch]$SkipBrowser,
    [switch]$BackendOnly,
    [switch]$FrontendOnly,
    [switch]$Verbose
)

# 設置控制台編碼
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎬 自動視頻剪輯平台 - PowerShell 啟動器" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 檢查執行策略
$executionPolicy = Get-ExecutionPolicy
if ($executionPolicy -eq "Restricted") {
    Write-Host "❌ PowerShell 執行策略受限" -ForegroundColor Red
    Write-Host "請以管理員身份運行以下命令後重試：" -ForegroundColor Yellow
    Write-Host "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Green
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查虛擬環境
if (-not (Test-Path "venv\Scripts\activate.bat")) {
    Write-Host "❌ 虛擬環境不存在，請先運行 setup_windows.bat" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查依賴
if (-not (Test-Path "backend\requirements.txt")) {
    Write-Host "❌ 後端配置文件不存在" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

if (-not (Test-Path "frontend\package.json")) {
    Write-Host "❌ 前端配置文件不存在" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

if (-not (Test-Path "frontend\node_modules")) {
    Write-Host "❌ 前端依賴未安裝，請先運行 setup_windows.bat" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 創建日誌目錄
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

# 函數：檢查端口是否被占用
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# 函數：等待服務啟動
function Wait-ForService {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$TimeoutSeconds = 30
    )
    
    Write-Host "⏳ 等待 $ServiceName 啟動..." -ForegroundColor Yellow
    
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    while ((Get-Date) -lt $timeout) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $ServiceName 啟動成功" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Start-Sleep -Seconds 2
        }
    }
    
    Write-Host "⚠️  $ServiceName 啟動超時" -ForegroundColor Yellow
    return $false
}

# 檢查端口占用
if (Test-Port 8000) {
    Write-Host "⚠️  端口 8000 已被占用，可能後端服務已在運行" -ForegroundColor Yellow
}

if (Test-Port 3000) {
    Write-Host "⚠️  端口 3000 已被占用，可能前端服務已在運行" -ForegroundColor Yellow
}

Write-Host "🚀 正在啟動服務..." -ForegroundColor Green
Write-Host ""

# 啟動後端服務
if (-not $FrontendOnly) {
    Write-Host "🔧 啟動後端服務..." -ForegroundColor Cyan
    
    $backendArgs = @(
        "/k"
        "cd backend && ..\venv\Scripts\activate.bat && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    )
    
    $backendProcess = Start-Process -FilePath "cmd.exe" -ArgumentList $backendArgs -WindowStyle Normal -PassThru
    
    if ($Verbose) {
        Write-Host "後端進程 ID: $($backendProcess.Id)" -ForegroundColor Gray
    }
    
    Start-Sleep -Seconds 3
    
    # 啟動任務隊列
    Write-Host "🔧 啟動任務隊列..." -ForegroundColor Cyan
    
    $hueyArgs = @(
        "/k"
        "cd backend && ..\venv\Scripts\activate.bat && python -m huey_consumer app.tasks.editing_tasks.huey"
    )
    
    $hueyProcess = Start-Process -FilePath "cmd.exe" -ArgumentList $hueyArgs -WindowStyle Normal -PassThru
    
    if ($Verbose) {
        Write-Host "任務隊列進程 ID: $($hueyProcess.Id)" -ForegroundColor Gray
    }
}

# 啟動前端服務
if (-not $BackendOnly) {
    Write-Host "🔧 啟動前端服務..." -ForegroundColor Cyan
    
    $frontendArgs = @(
        "/k"
        "cd frontend && npm run dev"
    )
    
    $frontendProcess = Start-Process -FilePath "cmd.exe" -ArgumentList $frontendArgs -WindowStyle Normal -PassThru
    
    if ($Verbose) {
        Write-Host "前端進程 ID: $($frontendProcess.Id)" -ForegroundColor Gray
    }
}

# 等待服務啟動
if (-not $FrontendOnly) {
    Wait-ForService -Url "http://localhost:8000/health" -ServiceName "後端服務"
}

if (-not $BackendOnly) {
    Start-Sleep -Seconds 5
    Wait-ForService -Url "http://localhost:3000" -ServiceName "前端服務"
}

Write-Host ""
Write-Host "🎉 系統啟動完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 服務信息：" -ForegroundColor Cyan

if (-not $FrontendOnly) {
    Write-Host "- 後端 API: http://localhost:8000" -ForegroundColor White
    Write-Host "- API 文檔: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "- 健康檢查: http://localhost:8000/health" -ForegroundColor White
}

if (-not $BackendOnly) {
    Write-Host "- 前端界面: http://localhost:3000" -ForegroundColor White
}

Write-Host ""
Write-Host "💡 使用說明：" -ForegroundColor Cyan
Write-Host "1. 在瀏覽器中打開 http://localhost:3000 使用系統" -ForegroundColor White
Write-Host "2. 查看 API 文檔: http://localhost:8000/docs" -ForegroundColor White
Write-Host "3. 運行測試: .\test_windows.ps1" -ForegroundColor White
Write-Host "4. 停止服務: .\stop_windows.ps1" -ForegroundColor White
Write-Host ""

# 自動打開瀏覽器
if (-not $SkipBrowser -and -not $BackendOnly) {
    $openBrowser = Read-Host "是否自動打開瀏覽器？ (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Write-Host "🌐 正在打開瀏覽器..." -ForegroundColor Green
        Start-Process "http://localhost:3000"
    }
}

Write-Host ""
Write-Host "📝 注意事項：" -ForegroundColor Yellow
Write-Host "- 請保持服務窗口開啟以監控狀態" -ForegroundColor White
Write-Host "- 使用 stop_windows.ps1 或 stop_windows.bat 停止服務" -ForegroundColor White
Write-Host "- 服務日誌會顯示在各自的命令行窗口中" -ForegroundColor White
Write-Host ""

Read-Host "按 Enter 鍵退出啟動器（服務將繼續運行）"
