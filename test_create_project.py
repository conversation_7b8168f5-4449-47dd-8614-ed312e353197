#!/usr/bin/env python3
"""Test create project API"""

import requests
import json

def test_create_project():
    """Test create project API"""
    print("🔍 Testing create project API...")
    
    # Test data
    project_data = {
        'name': 'Test Project',
        'description': 'Test Description',
        'status': 'active'
    }
    
    print(f"📋 Testing with data: {project_data}")
    
    try:
        response = requests.post('http://localhost:8000/api/projects/', json=project_data)
        print(f"📋 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        print(f"📋 Response text: {response.text}")
        
        if response.status_code == 200 or response.status_code == 201:
            result = response.json()
            print(f"✅ Project created successfully!")
            print(f"📋 Project ID: {result.get('id')}")
            print(f"📋 Project Name: {result.get('name')}")
        else:
            print(f"❌ Create project failed with status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error detail: {error_data}")
            except:
                print("📋 Could not parse error response")
                
    except Exception as e:
        print(f"❌ Create project API error: {e}")

if __name__ == "__main__":
    test_create_project()
