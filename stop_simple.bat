@echo off
echo ========================================
echo Auto Video Editor - Stop Services
echo ========================================
echo.

echo Stopping all services...

:: Kill Node.js processes (frontend)
echo Stopping frontend services...
taskkill /f /im node.exe >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: Frontend services stopped
) else (
    echo INFO: No frontend services were running
)

:: Kill Python processes (backend and task queue)
echo Stopping backend services...
taskkill /f /im python.exe >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: Backend services stopped
) else (
    echo INFO: No backend services were running
)

:: Release ports
echo Releasing ports...
for /f "tokens=5" %%a in ('netstat -ano ^| find ":8000"') do (
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -ano ^| find ":3000"') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo SUCCESS: All services stopped!
echo.
pause
