#!/usr/bin/env python3
"""
檢查項目和文件狀態
"""

import os
import sys
import requests

# 添加backend路徑到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def check_projects_via_api():
    """通過API檢查項目和文件"""
    base_url = "http://localhost:8000"
    
    print("📋 檢查項目和文件狀態")
    print("=" * 50)
    
    try:
        # 獲取項目列表
        response = requests.get(f"{base_url}/api/projects/")
        if response.status_code == 200:
            projects = response.json()
            print(f"總共找到 {len(projects)} 個項目:")
            
            for i, project in enumerate(projects[:5], 1):  # 只顯示前5個
                project_id = project['id']
                project_name = project['name']
                print(f"\n{i}. 項目: {project_name}")
                print(f"   ID: {project_id}")
                
                # 獲取項目詳情
                detail_response = requests.get(f"{base_url}/api/projects/{project_id}")
                if detail_response.status_code == 200:
                    detail = detail_response.json()
                    files = detail.get('files', [])
                    print(f"   文件數量: {len(files)}")
                    
                    if files:
                        video_files = [f for f in files if f.get('file_type') == 'video']
                        audio_files = [f for f in files if f.get('file_type') == 'audio']
                        subtitle_files = [f for f in files if f.get('file_type') == 'subtitle']
                        
                        print(f"   🎥 視頻: {len(video_files)} 個")
                        print(f"   🎵 音頻: {len(audio_files)} 個")
                        print(f"   📝 字幕: {len(subtitle_files)} 個")
                        
                        # 顯示文件詳情
                        for file_info in files[:3]:  # 只顯示前3個文件
                            print(f"      📄 {file_info['filename']} ({file_info.get('file_type', 'unknown')})")
                        
                        if len(files) > 3:
                            print(f"      ... 還有 {len(files) - 3} 個文件")
                        
                        # 如果有視頻文件，這個項目可以用來測試剪輯
                        if video_files:
                            print(f"   ✅ 可用於剪輯測試")
                            return project_id, project_name, video_files[0]
                    else:
                        print("   📭 無文件")
                else:
                    print(f"   ❌ 獲取項目詳情失敗: {detail_response.status_code}")
            
            return None, None, None
        else:
            print(f"❌ 獲取項目列表失敗: {response.status_code}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ 檢查項目錯誤: {e}")
        return None, None, None

def test_editing_with_project(project_id, project_name, video_file):
    """使用指定項目測試剪輯功能"""
    base_url = "http://localhost:8000"
    
    print(f"\n🎬 使用項目 '{project_name}' 測試剪輯功能")
    print("=" * 50)
    
    try:
        # 構建剪輯配置
        editing_config = {
            "project_id": project_id,
            "main_video_path": f"backend/storage/uploads/{project_id}/{video_file['filename']}",
            "subtitle_effect": "none",
            "background_volume": 0.3,
            "audio_volume": 1.0,
            "output_size": "original"
        }
        
        print("📋 剪輯配置:")
        print(f"   項目ID: {project_id}")
        print(f"   主視頻: {video_file['filename']}")
        print(f"   字幕效果: {editing_config['subtitle_effect']}")
        print(f"   輸出尺寸: {editing_config['output_size']}")
        
        # 發送剪輯請求
        print("\n🚀 發送剪輯請求...")
        response = requests.post(
            f"{base_url}/api/editing/start",
            json=editing_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            print(f"✅ 剪輯任務已創建!")
            print(f"   任務ID: {job_id}")
            print(f"   狀態: {result.get('status', 'unknown')}")
            
            print(f"\n💡 您可以通過以下方式監控任務:")
            print(f"   • 任務監控頁面: http://localhost:3001/tasks")
            print(f"   • 項目詳情頁面: http://localhost:3001/projects/{project_id}")
            print(f"   • API查詢: GET {base_url}/api/progress/{job_id}")
            
            return True
        else:
            print(f"❌ 創建剪輯任務失敗: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"錯誤詳情: {error_detail}")
            except:
                print(f"錯誤內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試剪輯功能錯誤: {e}")
        return False

def main():
    """主函數"""
    print("🎬 自動視頻剪輯平台 - 項目文件檢查")
    print("=" * 60)
    
    # 檢查項目和文件
    project_id, project_name, video_file = check_projects_via_api()
    
    if project_id and video_file:
        print(f"\n🎯 找到可用於測試的項目: {project_name}")
        
        # 詢問是否要測試剪輯功能
        print(f"\n📝 剪輯功能狀態:")
        print(f"   ✅ 後端API服務: 運行中")
        print(f"   ✅ Huey任務隊列: 運行中")
        print(f"   ✅ 測試項目: {project_name}")
        print(f"   ✅ 測試視頻: {video_file['filename']}")
        
        # 測試剪輯功能
        if test_editing_with_project(project_id, project_name, video_file):
            print(f"\n🎉 剪輯功能測試成功!")
            print(f"\n📋 您現在可以:")
            print(f"   1. 在前端創建新的剪輯任務")
            print(f"   2. 監控任務進度")
            print(f"   3. 下載剪輯結果")
            print(f"   4. 取消或重試失敗的任務")
        else:
            print(f"\n❌ 剪輯功能測試失敗")
    else:
        print(f"\n⚠️  沒有找到包含視頻文件的項目")
        print(f"\n💡 建議:")
        print(f"   1. 訪問 http://localhost:3001/upload 上傳視頻文件")
        print(f"   2. 或者訪問項目詳情頁面添加文件到現有項目")
        print(f"   3. 確保上傳的文件包含至少一個視頻文件 (.mp4, .avi, .mov 等)")

if __name__ == "__main__":
    main()
