<template>
  <div class="tasks-page">
    <div class="page-header">
      <h1>任務監控</h1>
      <div class="header-actions">
        <el-button @click="refreshTasks" :loading="tasksStore.loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自動刷新"
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <!-- 統計卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total || 0 }}</div>
                <div class="stat-label">總任務數</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending || 0 }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.processing || 0 }}</div>
                <div class="stat-label">處理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completed || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任務過濾器 -->
    <div class="filter-section">
      <el-card>
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="狀態">
            <el-select v-model="filterForm.status" placeholder="選擇狀態" clearable>
              <el-option label="等待中" value="pending" />
              <el-option label="處理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="失敗" value="failed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="項目ID">
            <el-input v-model="filterForm.project_id" placeholder="輸入項目ID" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadTasks">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 任務列表 -->
    <div class="tasks-list">
      <el-card v-loading="tasksStore.loading">
        <template #header>
          <div class="list-header">
            <span>任務列表</span>
            <el-tag v-if="activeTasks.length > 0" type="warning">
              {{ activeTasks.length }} 個活動任務
            </el-tag>
          </div>
        </template>

        <div v-if="tasks.length === 0" class="empty-state">
          <el-empty description="暫無任務" />
        </div>

        <el-table v-else :data="tasks" style="width: 100%">
          <el-table-column prop="id" label="任務ID" width="200">
            <template #default="{ row }">
              <el-link type="primary" @click="viewTaskDetail(row)">
                {{ row.id.substring(0, 8) }}...
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="project_id" label="項目ID" width="150">
            <template #default="{ row }">
              <el-link @click="viewProject(row.project_id)">
                {{ row.project_id ? row.project_id.substring(0, 8) + '...' : '-' }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="狀態" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="進度" width="150">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress || 0"
                :status="getProgressStatus(row.status)"
                :stroke-width="8"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click="viewTaskDetail(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'processing' || row.status === 'pending'"
                  size="small"
                  type="warning"
                  @click="cancelTask(row)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
                <el-button
                  v-if="row.status === 'failed'"
                  size="small"
                  type="success"
                  @click="retryTask(row)"
                >
                  <el-icon><RefreshRight /></el-icon>
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalTasks"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 任務詳情對話框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任務詳情"
      width="600px"
    >
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任務ID">
            {{ selectedTask.id }}
          </el-descriptions-item>
          <el-descriptions-item label="項目ID">
            {{ selectedTask.project_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="狀態">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="進度">
            {{ selectedTask.progress || 0 }}%
          </el-descriptions-item>
          <el-descriptions-item label="創建時間">
            {{ formatDate(selectedTask.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新時間">
            {{ formatDate(selectedTask.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="輸出路徑" span="2">
            {{ selectedTask.output_path || '-' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedTask.error" label="錯誤信息" span="2">
            <el-text type="danger">{{ selectedTask.error }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">關閉</el-button>
          <el-button
            v-if="selectedTask && selectedTask.output_path"
            type="primary"
            @click="downloadResult"
          >
            下載結果
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, DataBoard, Clock, Loading, Check, View, Close, RefreshRight
} from '@element-plus/icons-vue'

export default {
  name: 'Tasks',
  components: {
    Refresh, DataBoard, Clock, Loading, Check, View, Close, RefreshRight
  },
  setup() {
    const router = useRouter()
    const tasksStore = useTasksStore()

    // 狀態
    const autoRefresh = ref(false)
    const detailDialogVisible = ref(false)
    const selectedTask = ref(null)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalTasks = ref(0)
    const refreshInterval = ref(null)

    // 過濾表單
    const filterForm = reactive({
      status: '',
      project_id: ''
    })

    // 計算屬性
    const tasks = computed(() => tasksStore.tasks)
    const stats = computed(() => tasksStore.stats)
    const activeTasks = computed(() => tasksStore.activeTasks)

    // 方法
    const loadTasks = async () => {
      try {
        const params = {
          page: currentPage.value,
          limit: pageSize.value,
          ...filterForm
        }
        const response = await tasksStore.fetchTasks(params)
        totalTasks.value = response.total || 0
      } catch (error) {
        ElMessage.error('加載任務失敗')
      }
    }

    const loadStats = async () => {
      try {
        await tasksStore.fetchStats()
      } catch (error) {
        console.error('Failed to load stats:', error)
      }
    }

    const refreshTasks = async () => {
      await Promise.all([loadTasks(), loadStats()])
    }

    const resetFilter = () => {
      filterForm.status = ''
      filterForm.project_id = ''
      loadTasks()
    }

    const handleSizeChange = (size) => {
      pageSize.value = size
      loadTasks()
    }

    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadTasks()
    }

    const toggleAutoRefresh = (enabled) => {
      if (enabled) {
        refreshInterval.value = setInterval(refreshTasks, 3000) // 每3秒刷新
      } else {
        if (refreshInterval.value) {
          clearInterval(refreshInterval.value)
          refreshInterval.value = null
        }
      }
    }

    const viewTaskDetail = (task) => {
      selectedTask.value = task
      detailDialogVisible.value = true
    }

    const viewProject = (projectId) => {
      if (projectId) {
        router.push(`/projects/${projectId}`)
      }
    }

    const cancelTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          `確定要取消任務 "${task.id.substring(0, 8)}..." 嗎？`,
          '確認取消',
          {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await tasksStore.cancelTask(task.id)
        ElMessage.success('任務已取消')
        refreshTasks()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('取消任務失敗')
        }
      }
    }

    const retryTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          `確定要重試任務 "${task.id.substring(0, 8)}..." 嗎？`,
          '確認重試',
          {
            confirmButtonText: '確定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        await tasksStore.retryTask(task.id)
        ElMessage.success('任務已重新開始')
        refreshTasks()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('重試任務失敗')
        }
      }
    }

    const downloadResult = () => {
      if (selectedTask.value && selectedTask.value.output_path) {
        // 這裡應該實現文件下載邏輯
        ElMessage.info('下載功能待實現')
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-TW')
    }

    const getStatusType = (status) => {
      const statusMap = {
        'pending': 'info',
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'cancelled': 'info'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '等待中',
        'processing': '處理中',
        'completed': '已完成',
        'failed': '失敗',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return undefined
    }

    onMounted(() => {
      refreshTasks()
      // 如果有活動任務，自動開啟輪詢
      if (activeTasks.value.length > 0) {
        autoRefresh.value = true
        toggleAutoRefresh(true)
      }
    })

    onUnmounted(() => {
      if (refreshInterval.value) {
        clearInterval(refreshInterval.value)
      }
    })

    return {
      tasksStore,
      tasks,
      stats,
      activeTasks,
      autoRefresh,
      detailDialogVisible,
      selectedTask,
      filterForm,
      currentPage,
      pageSize,
      totalTasks,
      loadTasks,
      refreshTasks,
      resetFilter,
      handleSizeChange,
      handleCurrentChange,
      toggleAutoRefresh,
      viewTaskDetail,
      viewProject,
      cancelTask,
      retryTask,
      downloadResult,
      formatDate,
      getStatusType,
      getStatusText,
      getProgressStatus
    }
  }
}
</script>

<style scoped>
.tasks-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.task-detail {
  margin: 20px 0;
}
</style>
