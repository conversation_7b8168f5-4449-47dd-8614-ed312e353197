from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.routes import projects, uploads, editing, progress, files
from app.core.database import init_db
from app.core.config import settings

app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="自動視頻剪輯平台 API",
    debug=settings.debug
)

# CORS設置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化數據庫
@app.on_event("startup")
async def startup():
    print(f"Starting {settings.app_name} v{settings.app_version}")
    init_db()
    print("Database initialized successfully")

@app.on_event("shutdown")
async def shutdown():
    print("Shutting down Auto Video Editor API")

# 健康檢查端點
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version
    }

# 註冊路由
app.include_router(uploads.router, prefix=f"{settings.api_prefix}/uploads", tags=["uploads"])
app.include_router(projects.router, prefix=f"{settings.api_prefix}/projects", tags=["projects"])
app.include_router(editing.router, prefix=f"{settings.api_prefix}/editing", tags=["editing"])
app.include_router(progress.router, prefix=f"{settings.api_prefix}/progress", tags=["progress"])
app.include_router(files.router, prefix=f"{settings.api_prefix}/files", tags=["files"])

# 根路由
@app.get("/")
async def root():
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health"
    }