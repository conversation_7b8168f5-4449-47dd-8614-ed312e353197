from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.routes import projects, uploads, editing, progress
from app.core.database import init_db

app = FastAPI(title="Auto Video Editor API")

# CORS設置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化數據庫
@app.on_event("startup")
async def startup():
    init_db()

# 註冊路由
app.include_router(uploads.router, prefix="/api/uploads", tags=["uploads"])
app.include_router(projects.router, prefix="/api/projects", tags=["projects"])
app.include_router(editing.router, prefix="/api/editing", tags=["editing"])
app.include_router(progress.router, prefix="/api/progress", tags=["progress"])