from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from app.models.project import (
    Project, ProjectCreate, ProjectResponse,
    create_project, get_project, get_all_projects,
    update_project, delete_project, get_project_files
)
from app.models.job import get_jobs_by_project
from app.api.dependencies import get_database, validate_project_exists

router = APIRouter()

@router.post("/", response_model=dict)
async def create_new_project(project_data: ProjectCreate):
    """創建新項目"""
    try:
        project_id = create_project(project_data)
        project = get_project(project_id)
        
        return {
            "message": "Project created successfully",
            "project_id": project_id,
            "project": project
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")

@router.get("/", response_model=List[dict])
async def list_projects(
    name: Optional[str] = Query(None, description="Filter by project name (partial match)"),
    status: Optional[str] = Query(None, description="Filter by project status"),
    limit: Optional[int] = Query(None, description="Limit number of results"),
    offset: Optional[int] = Query(0, description="Offset for pagination")
):
    """獲取項目列表"""
    try:
        projects = get_all_projects()

        # 按名稱過濾（部分匹配，不區分大小寫）
        if name:
            name_lower = name.lower()
            projects = [p for p in projects if name_lower in p.get('name', '').lower()]

        # 按狀態過濾
        if status:
            projects = [p for p in projects if p.get('status') == status]

        # 分頁
        if limit:
            projects = projects[offset:offset + limit]
        elif offset:
            projects = projects[offset:]

        return projects
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve projects: {str(e)}")

@router.get("/{project_id}", response_model=dict)
async def get_project_detail(
    project_id: str,
    include_jobs: bool = Query(False, description="Include project jobs in response")
):
    """獲取項目詳情"""
    try:
        project = get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # 如果需要包含任務信息
        if include_jobs:
            jobs = get_jobs_by_project(project_id)
            project['jobs'] = jobs
        
        return project
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project: {str(e)}")

@router.put("/{project_id}", response_model=dict)
async def update_project_info(
    project_id: str,
    update_data: dict,
    project: dict = Depends(validate_project_exists)
):
    """更新項目信息"""
    try:
        # 只允許更新特定字段
        allowed_fields = {'name', 'description', 'status'}
        filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}
        
        if not filtered_data:
            raise HTTPException(status_code=400, detail="No valid fields to update")
        
        success = update_project(project_id, filtered_data)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update project")
        
        # 返回更新後的項目信息
        updated_project = get_project(project_id)
        return {
            "message": "Project updated successfully",
            "project": updated_project
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")

@router.delete("/{project_id}", response_model=dict)
async def delete_project_by_id(
    project_id: str,
    project: dict = Depends(validate_project_exists)
):
    """刪除項目（軟刪除）"""
    try:
        success = delete_project(project_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete project")
        
        return {
            "message": "Project deleted successfully",
            "project_id": project_id
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")

@router.get("/{project_id}/files", response_model=List[dict])
async def get_project_files_list(
    project_id: str,
    project: dict = Depends(validate_project_exists)
):
    """獲取項目文件列表"""
    try:
        files = get_project_files(project_id)
        return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project files: {str(e)}")

@router.get("/{project_id}/jobs", response_model=List[dict])
async def get_project_jobs_list(
    project_id: str,
    status: Optional[str] = Query(None, description="Filter jobs by status"),
    project: dict = Depends(validate_project_exists)
):
    """獲取項目的任務列表"""
    try:
        jobs = get_jobs_by_project(project_id)
        
        # 按狀態過濾
        if status:
            jobs = [job for job in jobs if job.get('status') == status]
        
        return jobs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project jobs: {str(e)}")

@router.get("/{project_id}/stats", response_model=dict)
async def get_project_statistics(
    project_id: str,
    project: dict = Depends(validate_project_exists)
):
    """獲取項目統計信息"""
    try:
        files = get_project_files(project_id)
        jobs = get_jobs_by_project(project_id)
        
        # 計算統計信息
        stats = {
            "total_files": len(files),
            "total_jobs": len(jobs),
            "job_status_counts": {},
            "file_type_counts": {},
            "total_file_size": 0
        }
        
        # 統計任務狀態
        for job in jobs:
            status = job.get('status', 'unknown')
            stats["job_status_counts"][status] = stats["job_status_counts"].get(status, 0) + 1
        
        # 統計文件類型和大小
        for file in files:
            file_type = file.get('file_type', 'unknown')
            stats["file_type_counts"][file_type] = stats["file_type_counts"].get(file_type, 0) + 1
            
            if file.get('file_size'):
                stats["total_file_size"] += file['file_size']
        
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project statistics: {str(e)}")
