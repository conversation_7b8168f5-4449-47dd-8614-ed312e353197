#!/usr/bin/env python3
"""
測試前端編輯功能
"""

import requests
import json
import time

def test_frontend_editing_api():
    """測試前端編輯API調用"""
    base_url = "http://localhost:8000"
    
    print("🎬 測試前端編輯功能")
    print("=" * 50)
    
    # 項目信息
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    # 模擬前端發送的編輯配置（使用文件名而不是完整路徑）
    editing_config = {
        "project_id": project_id,
        "main_video_filename": "0.MOV",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "output_size": "original"
    }
    
    print("📋 前端編輯配置:")
    for key, value in editing_config.items():
        print(f"   {key}: {value}")
    
    try:
        # 發送編輯請求
        print(f"\n🚀 發送前端編輯請求...")
        response = requests.post(
            f"{base_url}/api/editing/start",
            json=editing_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            print(f"✅ 編輯任務已創建!")
            print(f"   任務ID: {job_id}")
            print(f"   狀態: {result.get('status', 'unknown')}")
            
            # 顯示後端處理後的配置
            config = result.get('config', {})
            print(f"\n📁 後端處理後的路徑:")
            if 'main_video_path' in config:
                print(f"   主視頻: {config['main_video_path']}")
            if 'background_music_path' in config:
                print(f"   背景音樂: {config['background_music_path']}")
            if 'subtitle_path' in config:
                print(f"   字幕: {config['subtitle_path']}")
            
            # 監控任務進度
            print(f"\n📊 監控任務進度...")
            for i in range(10):  # 最多檢查10次
                try:
                    progress_response = requests.get(f"{base_url}/api/progress/{job_id}")
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        status = progress_data.get('status', 'unknown')
                        progress = progress_data.get('progress', 0)
                        
                        print(f"   第{i+1}次檢查 - 狀態: {status}, 進度: {progress}%")
                        
                        if status == 'completed':
                            output_path = progress_data.get('output_path')
                            print(f"   🎉 編輯完成! 輸出: {output_path}")
                            return True
                        elif status == 'failed':
                            error = progress_data.get('error', 'Unknown error')
                            print(f"   ❌ 編輯失敗: {error}")
                            return False
                        elif status in ['pending', 'processing']:
                            time.sleep(3)  # 等待3秒
                        else:
                            print(f"   ⚠️  未知狀態: {status}")
                            time.sleep(3)
                    else:
                        print(f"   ❌ 獲取進度失敗: {progress_response.status_code}")
                        break
                except Exception as e:
                    print(f"   ❌ 監控錯誤: {e}")
                    break
            
            print(f"   ⏰ 監控結束，任務可能仍在進行中")
            return True
            
        else:
            print(f"❌ 創建編輯任務失敗: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"錯誤詳情: {error_detail}")
            except:
                print(f"錯誤內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試編輯功能錯誤: {e}")
        return False

def test_with_audio_and_subtitle():
    """測試包含音頻和字幕的編輯"""
    base_url = "http://localhost:8000"
    
    print(f"\n🎵 測試包含音頻和字幕的編輯")
    print("=" * 50)
    
    # 項目信息
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    # 包含音頻和字幕的編輯配置
    editing_config = {
        "project_id": project_id,
        "main_video_filename": "0.MOV",
        "background_music_filename": "0.wav",
        "subtitle_filename": "0.srt",
        "subtitle_effect": "karaoke",
        "background_volume": 0.2,
        "audio_volume": 1.2,
        "output_size": "1280x720"
    }
    
    print("📋 完整編輯配置:")
    for key, value in editing_config.items():
        print(f"   {key}: {value}")
    
    try:
        # 發送編輯請求
        print(f"\n🚀 發送完整編輯請求...")
        response = requests.post(
            f"{base_url}/api/editing/start",
            json=editing_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('job_id')
            print(f"✅ 完整編輯任務已創建!")
            print(f"   任務ID: {job_id}")
            
            # 顯示處理後的配置
            config = result.get('config', {})
            print(f"\n📁 處理後的文件路徑:")
            for key, value in config.items():
                if key.endswith('_path'):
                    print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ 創建完整編輯任務失敗: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"錯誤詳情: {error_detail}")
            except:
                print(f"錯誤內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試完整編輯功能錯誤: {e}")
        return False

def main():
    """主測試函數"""
    print("🎬 前端編輯功能測試")
    print("=" * 60)
    
    # 測試基本編輯功能
    success1 = test_frontend_editing_api()
    
    # 測試完整編輯功能
    success2 = test_with_audio_and_subtitle()
    
    print(f"\n📋 測試結果總結:")
    print(f"   基本編輯功能: {'✅ 通過' if success1 else '❌ 失敗'}")
    print(f"   完整編輯功能: {'✅ 通過' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print(f"\n🎉 前端編輯功能測試全部通過!")
        print(f"\n💡 現在您可以:")
        print(f"   1. 在項目詳情頁面點擊「開始編輯」")
        print(f"   2. 在「編輯配置」標籤頁調整參數")
        print(f"   3. 在「任務」標籤頁監控進度")
        print(f"   4. 在任務監控頁面查看所有任務")
    else:
        print(f"\n❌ 部分測試失敗，請檢查配置")

if __name__ == "__main__":
    main()
