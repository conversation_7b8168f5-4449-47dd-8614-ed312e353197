import os
import re
from typing import Optional, Dict, Any, List
import pysrt
import pysubs2
from app.core.config import settings

class SubtitleProcessor:
    """字幕處理服務"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.supported_formats = ['.srt', '.ass', '.ssa', '.vtt']
    
    def process_subtitle(self, subtitle_path: str, effect: str = "none", 
                        output_format: str = "ass") -> str:
        """處理字幕文件並應用特效"""
        try:
            # 檢查文件是否存在
            if not os.path.exists(subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {subtitle_path}")
            
            # 獲取文件擴展名
            file_ext = os.path.splitext(subtitle_path)[1].lower()
            
            if file_ext not in self.supported_formats:
                raise ValueError(f"Unsupported subtitle format: {file_ext}")
            
            # 根據輸出格式處理
            if output_format.lower() == "ass":
                return self._convert_to_ass(subtitle_path, effect)
            elif output_format.lower() == "srt":
                return self._convert_to_srt(subtitle_path)
            else:
                raise ValueError(f"Unsupported output format: {output_format}")
        
        except Exception as e:
            print(f"Error processing subtitle: {e}")
            raise
    
    def _convert_to_ass(self, subtitle_path: str, effect: str = "none") -> str:
        """轉換字幕為ASS格式並應用特效"""
        try:
            # 使用pysubs2加載字幕
            subs = pysubs2.load(subtitle_path)
            
            # 應用特效
            if effect != "none":
                subs = self._apply_subtitle_effect(subs, effect)
            
            # 生成輸出路徑 - 使用項目的上傳目錄
            output_dir = os.path.join(settings.upload_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, f"subtitles_{effect}.ass")
            
            # 保存ASS文件
            subs.save(output_path)
            
            return output_path
        
        except Exception as e:
            print(f"Error converting to ASS: {e}")
            raise
    
    def _convert_to_srt(self, subtitle_path: str) -> str:
        """轉換字幕為SRT格式"""
        try:
            # 使用pysubs2加載字幕
            subs = pysubs2.load(subtitle_path)
            
            # 生成輸出路徑 - 使用項目的上傳目錄
            output_dir = os.path.join(settings.upload_dir, self.project_id)
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, "subtitles.srt")
            
            # 保存SRT文件
            subs.save(output_path)
            
            return output_path
        
        except Exception as e:
            print(f"Error converting to SRT: {e}")
            raise
    
    def _apply_subtitle_effect(self, subs: pysubs2.SSAFile, effect: str) -> pysubs2.SSAFile:
        """應用字幕特效"""
        try:
            if effect == "karaoke":
                return self._apply_karaoke_effect(subs)
            elif effect == "typewriter":
                return self._apply_typewriter_effect(subs)
            elif effect == "glow":
                return self._apply_glow_effect(subs)
            elif effect == "shadow":
                return self._apply_shadow_effect(subs)
            else:
                return subs
        
        except Exception as e:
            print(f"Error applying effect {effect}: {e}")
            return subs
    
    def _apply_karaoke_effect(self, subs: pysubs2.SSAFile) -> pysubs2.SSAFile:
        """應用卡拉OK特效"""
        # 設置ASS樣式
        style = subs.styles.get("Default", pysubs2.SSAStyle())
        style.fontname = settings.default_subtitle_font
        style.fontsize = settings.default_subtitle_size
        style.primarycolour = pysubs2.Color(255, 255, 255)  # 白色
        style.secondarycolour = pysubs2.Color(255, 0, 0)    # 紅色
        style.outline = 2
        style.shadow = 1
        
        # 為每行字幕添加卡拉OK標記
        for line in subs:
            # 簡單的卡拉OK效果：使用{\k}標記
            words = line.text.split()
            karaoke_text = ""
            for i, word in enumerate(words):
                # 每個詞持續時間約為總時間除以詞數
                duration = (line.end - line.start) // len(words)
                karaoke_text += f"{{\\k{duration//10}}}{word} "
            
            line.text = karaoke_text.strip()
        
        return subs
    
    def _apply_typewriter_effect(self, subs: pysubs2.SSAFile) -> pysubs2.SSAFile:
        """應用打字機特效"""
        # 設置樣式
        style = subs.styles.get("Default", pysubs2.SSAStyle())
        style.fontname = "Courier New"
        style.fontsize = settings.default_subtitle_size
        style.primarycolour = pysubs2.Color(0, 255, 0)  # 綠色
        
        # 為每行字幕添加打字機效果
        for line in subs:
            # 使用{\t}標記創建逐字顯示效果
            text = line.text
            duration_per_char = (line.end - line.start) // len(text)
            
            typewriter_text = ""
            for i, char in enumerate(text):
                start_time = i * duration_per_char
                typewriter_text += f"{{\\t({start_time},{start_time + duration_per_char},\\alpha&HFF&\\alpha&H00&)}}{char}"
            
            line.text = typewriter_text
        
        return subs
    
    def _apply_glow_effect(self, subs: pysubs2.SSAFile) -> pysubs2.SSAFile:
        """應用發光特效"""
        style = subs.styles.get("Default", pysubs2.SSAStyle())
        style.fontname = settings.default_subtitle_font
        style.fontsize = settings.default_subtitle_size
        style.primarycolour = pysubs2.Color(255, 255, 255)  # 白色
        style.outline = 3
        style.shadow = 2
        style.borderstyle = 3  # 發光邊框
        
        # 添加發光動畫
        for line in subs:
            line.text = f"{{\\blur3\\bord3}}{line.text}"
        
        return subs
    
    def _apply_shadow_effect(self, subs: pysubs2.SSAFile) -> pysubs2.SSAFile:
        """應用陰影特效"""
        style = subs.styles.get("Default", pysubs2.SSAStyle())
        style.fontname = settings.default_subtitle_font
        style.fontsize = settings.default_subtitle_size
        style.primarycolour = pysubs2.Color(255, 255, 255)  # 白色
        style.outline = 2
        style.shadow = 3
        
        # 添加陰影效果
        for line in subs:
            line.text = f"{{\\shad3\\bord2}}{line.text}"
        
        return subs
    
    def get_subtitle_info(self, subtitle_path: str) -> Dict[str, Any]:
        """獲取字幕文件信息"""
        try:
            if not os.path.exists(subtitle_path):
                raise FileNotFoundError(f"Subtitle file not found: {subtitle_path}")
            
            subs = pysubs2.load(subtitle_path)
            
            return {
                "format": os.path.splitext(subtitle_path)[1].lower(),
                "total_lines": len(subs),
                "duration": subs[-1].end if subs else 0,
                "styles": list(subs.styles.keys()) if hasattr(subs, 'styles') else [],
                "file_size": os.path.getsize(subtitle_path)
            }
        
        except Exception as e:
            print(f"Error getting subtitle info: {e}")
            return {}

# 向後兼容的函數
def convert_srt_to_ass(srt_path: str, project_id: str) -> str:
    """轉換SRT到ASS格式（向後兼容）"""
    processor = SubtitleProcessor(project_id)
    return processor.process_subtitle(srt_path, output_format="ass")

def apply_subtitle_effect(subtitle_path: str, effect: str, project_id: str) -> str:
    """應用字幕特效（向後兼容）"""
    processor = SubtitleProcessor(project_id)
    return processor.process_subtitle(subtitle_path, effect=effect)
