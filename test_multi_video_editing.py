#!/usr/bin/env python3
"""
測試多選片中視頻功能
"""
import requests
import json
import time

def test_multi_video_editing():
    """測試多選片中視頻編輯功能"""
    base_url = "http://localhost:8000"
    
    print("🎬 測試多選片中視頻編輯功能")
    print("=" * 50)
    
    # 1. 檢查項目和文件
    print("📁 檢查項目文件...")
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    response = requests.get(f"{base_url}/api/projects/{project_id}")
    if response.status_code != 200:
        print(f"❌ 無法獲取項目: {response.status_code}")
        return
    
    project = response.json()
    print(f"✅ 項目: {project['name']}")
    
    # 獲取項目文件
    response = requests.get(f"{base_url}/api/projects/{project_id}/files")
    if response.status_code != 200:
        print(f"❌ 無法獲取項目文件: {response.status_code}")
        return
    
    files = response.json()
    video_files = [f for f in files if f['file_type'] == 'video']
    
    print(f"📹 找到 {len(video_files)} 個視頻文件:")
    for i, file in enumerate(video_files[:5]):  # 只顯示前5個
        print(f"  {i+1}. {file['filename']} ({file['file_size']} bytes)")
    
    if len(video_files) < 2:
        print("❌ 需要至少2個視頻文件來測試多選功能")
        return
    
    # 2. 測試多選片中視頻配置
    print("\n🎯 測試多選片中視頻配置...")
    
    # 選擇多個片中視頻
    middle_videos = [video_files[0]['filename'], video_files[1]['filename']]
    if len(video_files) >= 3:
        middle_videos.append(video_files[2]['filename'])
    
    config = {
        "project_id": project_id,
        "middle_video_filenames": middle_videos,  # 多選片中視頻
        "output_size": "1080x1920",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"📝 編輯配置:")
    print(f"  - 片中視頻: {middle_videos}")
    print(f"  - 輸出尺寸: {config['output_size']}")
    print(f"  - 音頻音量: {config['audio_volume']}")
    
    # 3. 啟動編輯任務
    print("\n🚀 啟動多視頻編輯任務...")
    
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    if response.status_code != 200:
        print(f"❌ 啟動編輯失敗: {response.status_code}")
        print(f"錯誤: {response.text}")
        return
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ 任務已啟動，ID: {job_id}")
    
    # 4. 監控進度
    print("\n📊 監控編輯進度...")
    
    for i in range(1, 21):  # 最多檢查20次
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            print(f"⚠️ 無法獲取進度: {response.status_code}")
            time.sleep(3)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        
        print(f"第{i:2d}次檢查 - 狀態: {status:10s} 進度: {progress:3d}%")
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"\n✅ 多視頻編輯完成!")
            print(f"📁 輸出文件: {output_path}")
            
            # 檢查輸出文件是否存在
            import os
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            else:
                print(f"⚠️ 輸出文件不存在: {output_path}")
            
            return True
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"\n❌ 編輯失敗: {error}")
            return False
        
        if i < 20:
            time.sleep(3)
    
    print("\n⏰ 編輯超時，但任務可能仍在後台運行")
    return False

if __name__ == "__main__":
    success = test_multi_video_editing()
    if success:
        print("\n🎉 多選片中視頻功能測試成功!")
    else:
        print("\n💥 多選片中視頻功能測試失敗!")
