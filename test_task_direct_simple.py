#!/usr/bin/env python3
"""Direct task test"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_task_direct():
    """Test task directly without API"""
    print("🔍 Testing task directly...")
    
    try:
        # Import task
        from app.tasks.editing_tasks import process_video_task
        
        # Create test data
        job_id = "test-job-123"
        main_video_path = "nonexistent.mp4"
        config = {
            'project_id': 'b9da6931-653e-4a2c-a575-a26dadeb6c08',
            'subtitle_effect': 'none',
            'background_volume': 0.3,
            'audio_volume': 1.0
        }
        
        print(f"📋 Task type: {type(process_video_task)}")
        print(f"📋 Task attributes: {[attr for attr in dir(process_video_task) if not attr.startswith('_')]}")
        
        # Try direct call
        print("📋 Testing direct call...")
        try:
            result = process_video_task(job_id, main_video_path, config)
            print(f"✅ Direct call result: {result}")
        except Exception as e:
            print(f"❌ Direct call failed: {e}")
            print(f"📋 Error type: {type(e)}")
            
        # Try schedule call
        print("📋 Testing schedule call...")
        try:
            result = process_video_task.schedule(args=(job_id, main_video_path, config))
            print(f"✅ Schedule call result: {result}")
        except Exception as e:
            print(f"❌ Schedule call failed: {e}")
            print(f"📋 Error type: {type(e)}")
            
    except Exception as e:
        print(f"❌ Import or setup error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_task_direct()
