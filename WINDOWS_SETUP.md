# 🎬 自動視頻剪輯平台 - Windows 安裝指南

## 🚀 一鍵安裝和啟動

### 步驟 1: 環境準備

在開始之前，請確保您的 Windows 系統已安裝以下軟件：

#### 必需軟件
- **Python 3.8+** - [下載地址](https://www.python.org/downloads/)
- **Node.js 16+** - [下載地址](https://nodejs.org/)
- **Git** - [下載地址](https://git-scm.com/download/win)

#### 可選軟件（建議安裝）
- **FFmpeg** - [下載地址](https://ffmpeg.org/download.html)
  - 或使用 Chocolatey: `choco install ffmpeg`
  - 或使用 Scoop: `scoop install ffmpeg`

### 步驟 2: 下載項目

```bash
git clone <repository-url>
cd auto-video-editor
```

### 步驟 3: 一鍵設置環境

雙擊運行 `setup_windows.bat` 或在命令行中執行：

```cmd
setup_windows.bat
```

這個腳本會自動：
- ✅ 檢查 Python 和 Node.js 環境
- ✅ 創建 Python 虛擬環境
- ✅ 安裝所有後端依賴
- ✅ 安裝所有前端依賴
- ✅ 創建必要的目錄結構
- ✅ 初始化數據庫
- ✅ 創建配置文件

### 步驟 4: 一鍵啟動系統

雙擊運行 `start_windows.bat` 或在命令行中執行：

```cmd
start_windows.bat
```

這個腳本會自動：
- 🚀 啟動後端 API 服務 (端口 8000)
- 🚀 啟動任務隊列服務
- 🚀 啟動前端開發服務器 (端口 3000)
- 🌐 可選擇自動打開瀏覽器

## 📋 可用的批處理腳本

| 腳本文件 | 功能描述 |
|---------|---------|
| `setup_windows.bat` | 一鍵環境設置和依賴安裝 |
| `start_windows.bat` | 一鍵啟動所有服務 |
| `stop_windows.bat` | 一鍵停止所有服務 |
| `test_windows.bat` | 運行系統測試 |
| `run_backend.bat` | 僅啟動後端服務 |
| `run_frontend.bat` | 僅啟動前端服務 |
| `run_huey.bat` | 僅啟動任務隊列 |

## 🔧 手動啟動（高級用戶）

如果您需要更精細的控制，可以手動啟動各個服務：

### 啟動後端
```cmd
# 激活虛擬環境
venv\Scripts\activate.bat

# 啟動後端
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 啟動任務隊列
```cmd
# 新開命令行窗口
venv\Scripts\activate.bat
cd backend
python -m huey_consumer app.tasks.editing_tasks.huey
```

### 啟動前端
```cmd
# 新開命令行窗口
cd frontend
npm run dev
```

## 🧪 測試系統

運行 `test_windows.bat` 來測試系統是否正常工作：

```cmd
test_windows.bat
```

測試包括：
- ✅ 環境檢查
- ✅ 依賴驗證
- ✅ 數據庫連接
- ✅ API 端點測試
- ✅ 文件上傳測試

## 🌐 訪問應用

啟動成功後，您可以通過以下地址訪問：

- **前端界面**: http://localhost:3000
- **後端 API**: http://localhost:8000
- **API 文檔**: http://localhost:8000/docs
- **健康檢查**: http://localhost:8000/health

## 🛠️ 故障排除

### 常見問題

#### 1. Python 未找到
```
❌ Python 未安裝或未添加到 PATH
```
**解決方案**: 
- 安裝 Python 3.8+ 並確保勾選 "Add Python to PATH"
- 或手動添加 Python 到系統環境變量

#### 2. Node.js 未找到
```
❌ Node.js 未安裝或未添加到 PATH
```
**解決方案**: 
- 安裝 Node.js 16+ 
- 重啟命令行窗口

#### 3. 端口被占用
```
❌ 端口 8000 或 3000 被占用
```
**解決方案**: 
- 運行 `stop_windows.bat` 停止所有服務
- 或手動結束占用端口的進程

#### 4. 虛擬環境創建失敗
```
❌ 虛擬環境創建失敗
```
**解決方案**: 
- 確保有足夠的磁盤空間
- 以管理員身份運行 `setup_windows.bat`
- 檢查防病毒軟件是否阻止了文件創建

#### 5. 依賴安裝失敗
```
❌ 後端依賴安裝失敗
```
**解決方案**: 
- 檢查網絡連接
- 嘗試使用國內鏡像：
  ```cmd
  pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
  ```

#### 6. FFmpeg 相關錯誤
```
⚠️ FFmpeg 未安裝
```
**解決方案**: 
- 下載 FFmpeg 並添加到 PATH
- 或使用包管理器安裝：
  ```cmd
  # 使用 Chocolatey
  choco install ffmpeg
  
  # 使用 Scoop
  scoop install ffmpeg
  ```

### 檢查系統狀態

運行以下命令檢查各個組件的狀態：

```cmd
# 檢查 Python
python --version

# 檢查 Node.js
node --version

# 檢查 npm
npm --version

# 檢查 FFmpeg（可選）
ffmpeg -version

# 檢查端口占用
netstat -ano | find ":8000"
netstat -ano | find ":3000"
```

## 📁 目錄結構

```
auto-video-editor/
├── setup_windows.bat      # 環境設置腳本
├── start_windows.bat      # 一鍵啟動腳本
├── stop_windows.bat       # 停止服務腳本
├── test_windows.bat       # 測試腳本
├── run_backend.bat        # 後端啟動腳本
├── run_frontend.bat       # 前端啟動腳本
├── run_huey.bat          # 任務隊列啟動腳本
├── venv/                 # Python 虛擬環境
├── backend/              # 後端代碼
├── frontend/             # 前端代碼
├── storage/              # 文件存儲
│   ├── uploads/          # 上傳文件
│   ├── projects/         # 項目文件
│   ├── outputs/          # 輸出文件
│   └── temp/             # 臨時文件
└── logs/                 # 日誌文件（自動創建）
```

## 🎯 下一步

1. **運行測試**: `test_windows.bat`
2. **啟動系統**: `start_windows.bat`
3. **訪問應用**: http://localhost:3000
4. **查看文檔**: http://localhost:8000/docs

## 💡 提示

- 保持命令行窗口開啟以監控服務狀態
- 使用 `stop_windows.bat` 正確停止所有服務
- 定期運行 `test_windows.bat` 檢查系統健康狀態
- 查看各個服務窗口的日誌輸出來診斷問題

## 📞 獲取幫助

如果遇到問題：
1. 首先運行 `test_windows.bat` 檢查系統狀態
2. 查看本文檔的故障排除部分
3. 檢查各個服務窗口的錯誤信息
4. 提交 Issue 並附上錯誤信息
