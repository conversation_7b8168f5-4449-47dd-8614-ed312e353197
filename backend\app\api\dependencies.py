from fastapi import HTTPException, Depends, UploadFile
from typing import Optional, List
import os
from app.core.config import settings
from app.core.database import get_db, Database
from app.models.project import get_project
from app.models.job import get_job

def get_database() -> Database:
    """獲取數據庫實例"""
    return get_db()

def validate_project_exists(project_id: str, db: Database = Depends(get_database)) -> dict:
    """驗證項目是否存在"""
    project = get_project(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

def validate_job_exists(job_id: str, db: Database = Depends(get_database)) -> dict:
    """驗證任務是否存在"""
    job = get_job(job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job

def validate_file_upload(files: List[UploadFile]) -> List[UploadFile]:
    """驗證上傳的文件"""
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    for file in files:
        # 檢查文件大小
        if hasattr(file, 'size') and file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413, 
                detail=f"File {file.filename} is too large. Maximum size is {settings.max_file_size} bytes"
            )
        
        # 檢查文件類型
        allowed_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.srt', '.ass', '.wav', '.mp3', '.aac'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} is not supported. Allowed types: {', '.join(allowed_extensions)}"
            )
    
    return files

def validate_video_file(file_path: str) -> str:
    """驗證視頻文件是否存在且有效"""
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"Video file not found: {file_path}")
    
    # 檢查文件擴展名
    video_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.webm'}
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext not in video_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid video file format: {file_ext}. Supported formats: {', '.join(video_extensions)}"
        )
    
    return file_path

def validate_subtitle_file(file_path: str) -> str:
    """驗證字幕文件是否存在且有效"""
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"Subtitle file not found: {file_path}")
    
    # 檢查文件擴展名
    subtitle_extensions = {'.srt', '.ass', '.ssa', '.vtt'}
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext not in subtitle_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid subtitle file format: {file_ext}. Supported formats: {', '.join(subtitle_extensions)}"
        )
    
    return file_path

def validate_audio_file(file_path: str) -> str:
    """驗證音頻文件是否存在且有效"""
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"Audio file not found: {file_path}")
    
    # 檢查文件擴展名
    audio_extensions = {'.wav', '.mp3', '.aac', '.flac', '.ogg', '.m4a'}
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext not in audio_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid audio file format: {file_ext}. Supported formats: {', '.join(audio_extensions)}"
        )
    
    return file_path

def validate_editing_config(config: dict) -> dict:
    """驗證編輯配置"""
    required_fields = ['project_id', 'main_video_path']
    
    for field in required_fields:
        if field not in config:
            raise HTTPException(status_code=400, detail=f"Missing required field: {field}")
    
    # 驗證主視頻文件
    validate_video_file(config['main_video_path'])
    
    # 驗證可選的字幕文件
    if config.get('subtitle_path'):
        validate_subtitle_file(config['subtitle_path'])
    
    # 驗證可選的音頻文件
    if config.get('audio_path'):
        validate_audio_file(config['audio_path'])
    
    # 驗證可選的片頭片尾文件
    if config.get('intro_path'):
        validate_video_file(config['intro_path'])
    
    if config.get('outro_path'):
        validate_video_file(config['outro_path'])
    
    # 驗證字幕特效類型
    if config.get('subtitle_effect'):
        allowed_effects = ['none', 'karaoke', 'typewriter', 'glow', 'shadow']
        if config['subtitle_effect'] not in allowed_effects:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid subtitle effect: {config['subtitle_effect']}. Allowed effects: {', '.join(allowed_effects)}"
            )
    
    return config

def get_current_settings():
    """獲取當前應用設置"""
    return settings
