#!/usr/bin/env python3
"""
測試視頻尺寸調整功能
"""

import requests
import time
import json

def test_video_sizes():
    """測試不同的視頻尺寸設置"""
    base_url = "http://localhost:8000"
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    # 測試不同的尺寸配置
    size_configs = [
        {
            "name": "保持原始尺寸",
            "output_size": "original"
        },
        {
            "name": "Full HD 橫屏",
            "output_size": "1920x1080"
        },
        {
            "name": "豎屏 Full HD",
            "output_size": "1080x1920"
        },
        {
            "name": "方形 HD",
            "output_size": "720x720"
        },
        {
            "name": "4K UHD",
            "output_size": "3840x2160"
        }
    ]
    
    print("🎬 測試視頻尺寸調整功能")
    print("=" * 60)
    
    for i, size_config in enumerate(size_configs, 1):
        print(f"\n{i}. 測試 {size_config['name']} ({size_config['output_size']})")
        print("-" * 50)
        
        # 構建編輯配置
        editing_config = {
            "project_id": project_id,
            "main_video_filename": "0.MOV",
            "subtitle_effect": "none",
            "background_volume": 0.3,
            "audio_volume": 1.0,
            "output_size": size_config['output_size']
        }
        
        print(f"📋 配置: {json.dumps(editing_config, indent=2, ensure_ascii=False)}")
        
        try:
            # 發送編輯請求
            print("🚀 發送編輯請求...")
            response = requests.post(
                f"{base_url}/api/editing/start",
                json=editing_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                job_id = result.get('job_id')
                print(f"✅ 任務已創建! ID: {job_id}")
                
                # 監控任務進度
                print("📊 監控任務進度...")
                for check in range(1, 11):  # 最多檢查10次
                    time.sleep(3)
                    
                    progress_response = requests.get(f"{base_url}/api/progress/{job_id}")
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        status = progress_data.get('status', 'unknown')
                        progress = progress_data.get('progress', 0)
                        
                        print(f"   第{check}次檢查 - 狀態: {status}, 進度: {progress}%")
                        
                        if status == 'completed':
                            output_path = progress_data.get('output_path', 'N/A')
                            print(f"   ✅ 編輯完成! 輸出文件: {output_path}")
                            break
                        elif status == 'failed':
                            error = progress_data.get('error', 'Unknown error')
                            print(f"   ❌ 編輯失敗: {error}")
                            break
                    else:
                        print(f"   ⚠️ 無法獲取進度信息: {progress_response.status_code}")
                        break
                
            else:
                print(f"❌ 創建任務失敗: {response.status_code}")
                print(f"錯誤詳情: {response.text}")
                
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")
        
        # 如果不是最後一個測試，等待一下
        if i < len(size_configs):
            print("\n⏳ 等待5秒後進行下一個測試...")
            time.sleep(5)
    
    print("\n" + "=" * 60)
    print("🎯 視頻尺寸測試完成!")

if __name__ == "__main__":
    test_video_sizes()
