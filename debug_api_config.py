#!/usr/bin/env python3
"""
調試API配置傳遞
"""
import requests
import json

def debug_api_config():
    """調試API配置傳遞"""
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    base_url = "http://localhost:8000"
    
    print("🔍 調試API配置傳遞")
    print("=" * 50)
    
    # 構建測試配置
    config = {
        "project_id": project_id,
        
        # 片頭、片中、片尾配置
        "intro_filename": "0.MOV",
        "middle_video_filenames": ["1.MOV", "2.MOV"],
        "outro_filename": "3.MOV",
        
        # 視頻設置
        "output_size": "1080x1920",
        
        # 音頻設置
        "audio_volume": 0.8,
        "background_volume": 0.3,
        "mute_original_audio": False,
        
        # 字幕設置
        "enable_subtitles": True,
        "subtitle_filename": "0.srt",
        "subtitle_effect": "karaoke",
        
        # 背景音樂
        "background_music_enabled": True,
        "background_music_filename": "0.wav"
    }
    
    print(f"📝 發送的配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    print(f"\n🚀 發送POST請求到 /api/editing/start")
    
    # 發送請求並捕獲詳細響應
    try:
        response = requests.post(f"{base_url}/api/editing/start", json=config)
        
        print(f"📊 響應狀態碼: {response.status_code}")
        print(f"📊 響應頭: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 請求成功")
            print(f"📝 響應內容:")
            for key, value in result.items():
                if key == "config":
                    print(f"   {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"      {sub_key}: {sub_value}")
                else:
                    print(f"   {key}: {value}")
            
            return result.get("job_id")
        else:
            print(f"❌ 請求失敗")
            print(f"錯誤內容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 請求異常: {e}")
        return None

if __name__ == "__main__":
    job_id = debug_api_config()
    
    if job_id:
        print(f"\n✅ 任務ID: {job_id}")
        print(f"💡 請檢查Huey消費者日誌以查看實際處理的配置")
    else:
        print(f"\n❌ 調試失敗")
