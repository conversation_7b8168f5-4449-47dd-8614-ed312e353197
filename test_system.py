#!/usr/bin/env python3
"""
自動視頻剪輯平台系統測試腳本
測試後端 API 和基本功能
"""

import requests
import json
import time
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def test_health_check():
    """測試健康檢查端點"""
    print("🔍 測試健康檢查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康檢查通過")
            return True
        else:
            print(f"❌ 健康檢查失敗: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康檢查錯誤: {e}")
        return False

def test_create_project():
    """測試創建項目"""
    print("\n🔍 測試創建項目...")
    try:
        project_data = {
            "name": "測試項目",
            "description": "這是一個測試項目",
            "status": "active"
        }
        response = requests.post(f"{API_BASE}/projects/", json=project_data)
        if response.status_code == 200:
            project = response.json()
            print(f"✅ 項目創建成功: {project['id']}")
            return project
        else:
            print(f"❌ 項目創建失敗: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 項目創建錯誤: {e}")
        return None

def test_get_projects():
    """測試獲取項目列表"""
    print("\n🔍 測試獲取項目列表...")
    try:
        response = requests.get(f"{API_BASE}/projects/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 獲取項目列表成功: {len(data.get('projects', []))} 個項目")
            return data
        else:
            print(f"❌ 獲取項目列表失敗: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 獲取項目列表錯誤: {e}")
        return None

def test_get_project_detail(project_id):
    """測試獲取項目詳情"""
    print(f"\n🔍 測試獲取項目詳情: {project_id}")
    try:
        response = requests.get(f"{API_BASE}/projects/{project_id}")
        if response.status_code == 200:
            project = response.json()
            print(f"✅ 獲取項目詳情成功: {project['name']}")
            return project
        else:
            print(f"❌ 獲取項目詳情失敗: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 獲取項目詳情錯誤: {e}")
        return None

def test_upload_file(project_id):
    """測試文件上傳"""
    print(f"\n🔍 測試文件上傳到項目: {project_id}")
    try:
        # 創建一個測試文件
        test_file_path = "test_video.txt"
        with open(test_file_path, "w") as f:
            f.write("這是一個測試文件，模擬視頻文件")
        
        with open(test_file_path, "rb") as f:
            files = {"files": ("test_video.mp4", f, "video/mp4")}
            data = {"project_id": project_id}
            response = requests.post(f"{API_BASE}/uploads/", files=files, data=data)
        
        # 清理測試文件
        os.remove(test_file_path)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文件上傳成功: {result}")
            return result
        else:
            print(f"❌ 文件上傳失敗: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 文件上傳錯誤: {e}")
        return None

def test_get_tasks():
    """測試獲取任務列表"""
    print("\n🔍 測試獲取任務列表...")
    try:
        response = requests.get(f"{API_BASE}/progress/tasks")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 獲取任務列表成功: {len(data.get('jobs', []))} 個任務")
            return data
        else:
            print(f"❌ 獲取任務列表失敗: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 獲取任務列表錯誤: {e}")
        return None

def test_get_stats():
    """測試獲取統計信息"""
    print("\n🔍 測試獲取統計信息...")
    try:
        response = requests.get(f"{API_BASE}/progress/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 獲取統計信息成功: {stats}")
            return stats
        else:
            print(f"❌ 獲取統計信息失敗: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 獲取統計信息錯誤: {e}")
        return None

def test_start_editing(project_id):
    """測試開始編輯任務"""
    print(f"\n🔍 測試開始編輯任務: {project_id}")
    try:
        editing_data = {
            "project_id": project_id,
            "main_video_path": f"storage/uploads/{project_id}/test_video.mp4",
            "subtitle_effect": "none",
            "background_volume": 0.3,
            "audio_volume": 1.0
        }
        response = requests.post(f"{API_BASE}/editing/start", json=editing_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 編輯任務開始成功: {result}")
            return result
        else:
            print(f"❌ 編輯任務開始失敗: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ 編輯任務開始錯誤: {e}")
        return None

def test_database_connection():
    """測試數據庫連接"""
    print("\n🔍 測試數據庫連接...")
    try:
        import sys
        import os

        # 添加後端路徑到 Python 路徑
        backend_path = os.path.join(os.getcwd(), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)

        from app.core.database import Database, init_db

        # 初始化數據庫
        init_db()
        print("✅ 數據庫初始化成功")

        # 測試數據庫連接
        db = Database()
        result = db.execute_query("SELECT 1")
        print("✅ 數據庫連接成功")
        return True
    except Exception as e:
        print(f"❌ 數據庫連接錯誤: {e}")
        return False

def test_storage_directories():
    """測試存儲目錄"""
    print("\n🔍 測試存儲目錄...")
    try:
        directories = [
            "storage",
            "storage/uploads",
            "storage/projects", 
            "storage/outputs",
            "storage/temp"
        ]
        
        for directory in directories:
            path = Path(directory)
            if path.exists():
                print(f"✅ 目錄存在: {directory}")
            else:
                print(f"⚠️  目錄不存在，正在創建: {directory}")
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ 目錄創建成功: {directory}")
        
        return True
    except Exception as e:
        print(f"❌ 存儲目錄測試錯誤: {e}")
        return False

def run_all_tests():
    """運行所有測試"""
    print("🚀 開始系統測試...\n")
    
    # 測試存儲目錄
    if not test_storage_directories():
        print("❌ 存儲目錄測試失敗，停止測試")
        return
    
    # 測試數據庫
    if not test_database_connection():
        print("❌ 數據庫測試失敗，停止測試")
        return
    
    # 測試健康檢查
    if not test_health_check():
        print("❌ 服務器未運行，請先啟動後端服務")
        print("運行命令: cd backend && python -m uvicorn app.main:app --reload")
        return
    
    # 測試項目相關功能
    project = test_create_project()
    if not project:
        print("❌ 項目創建失敗，停止測試")
        return
    
    project_id = project["id"]
    
    # 測試其他功能
    test_get_projects()
    test_get_project_detail(project_id)
    test_upload_file(project_id)
    test_get_tasks()
    test_get_stats()
    test_start_editing(project_id)
    
    print("\n🎉 系統測試完成！")
    print("\n📋 測試總結:")
    print("- 如果所有測試都通過，系統基本功能正常")
    print("- 如果有測試失敗，請檢查相應的配置和依賴")
    print("- 前端測試請訪問: http://localhost:3000")

if __name__ == "__main__":
    run_all_tests()
