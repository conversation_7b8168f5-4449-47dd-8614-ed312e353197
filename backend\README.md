# 自動視頻剪輯平台 - 後端

這是一個基於 FastAPI 的自動視頻剪輯平台後端服務，支持視頻上傳、編輯、字幕處理、音頻處理等功能。

## 功能特性

- 🎬 視頻剪輯與處理
- 📝 字幕處理與特效（卡拉OK、打字機、發光等）
- 🎵 音頻處理與背景音樂添加
- 🎭 片頭片尾添加
- 📊 任務進度跟踪
- 🔌 REST API 接口
- 🤖 支持 n8n 等自動化工具集成

## 技術棧

- **Web框架**: FastAPI
- **數據庫**: SQLite
- **任務隊列**: Huey
- **視頻處理**: MoviePy, FFmpeg
- **字幕處理**: pysrt, pysubs2

## 安裝部署

### 1. 環境準備

確保系統已安裝：
- Python 3.8+
- FFmpeg

### 2. 安裝依賴

```bash
# 創建虛擬環境
python -m venv venv

# 激活虛擬環境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt
```

### 3. 配置環境

```bash
# 複製配置文件
cp .env.example .env

# 編輯配置文件
# 根據需要修改 .env 文件中的配置
```

### 4. 初始化數據庫

```bash
python -m app.core.database
```

### 5. 啟動服務

```bash
# 啟動 API 服務
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 啟動任務工作進程（新終端）
python -m huey.bin.huey_consumer app.tasks.editing_tasks.huey
```

## API 文檔

啟動服務後，可以通過以下地址訪問 API 文檔：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要 API 端點

### 文件上傳
- `POST /api/uploads/` - 上傳文件

### 項目管理
- `GET /api/projects/` - 獲取項目列表
- `POST /api/projects/` - 創建項目
- `GET /api/projects/{project_id}` - 獲取項目詳情
- `PUT /api/projects/{project_id}` - 更新項目
- `DELETE /api/projects/{project_id}` - 刪除項目

### 視頻編輯
- `POST /api/editing/start` - 開始編輯任務
- `POST /api/editing/cancel/{job_id}` - 取消任務
- `POST /api/editing/retry/{job_id}` - 重試任務

### 進度跟踪
- `GET /api/progress/{job_id}` - 獲取任務進度
- `GET /api/progress/` - 獲取任務列表
- `GET /api/progress/stats/summary` - 獲取統計信息

## 使用示例

### 1. 上傳文件

```bash
curl -X POST "http://localhost:8000/api/uploads/" \
  -F "files=@video.mp4" \
  -F "files=@subtitles.srt" \
  -F "project_name=My Project"
```

### 2. 開始編輯任務

```bash
curl -X POST "http://localhost:8000/api/editing/start" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "your-project-id",
    "main_video_path": "storage/uploads/project-id/video.mp4",
    "subtitle_path": "storage/uploads/project-id/subtitles.srt",
    "subtitle_effect": "karaoke"
  }'
```

### 3. 查看進度

```bash
curl "http://localhost:8000/api/progress/your-job-id"
```

## 配置說明

主要配置項說明：

- `MAX_FILE_SIZE`: 最大文件上傳大小（字節）
- `MAX_CONCURRENT_TASKS`: 最大並發任務數
- `DEFAULT_VIDEO_CODEC`: 默認視頻編碼器
- `DEFAULT_AUDIO_CODEC`: 默認音頻編碼器

## 故障排除

### 常見問題

1. **FFmpeg 未找到**
   - 確保 FFmpeg 已安裝並在 PATH 中
   - 或在 .env 文件中設置 FFMPEG_PATH

2. **文件上傳失敗**
   - 檢查文件大小是否超過限制
   - 檢查文件格式是否支持

3. **任務處理失敗**
   - 檢查任務日誌
   - 確保輸入文件路徑正確

### 日誌查看

```bash
# 查看 API 服務日誌
tail -f logs/api.log

# 查看任務處理日誌
tail -f logs/tasks.log
```

## 開發

### 運行測試

```bash
pytest
```

### 代碼格式化

```bash
black app/
isort app/
```

## 許可證

MIT License
