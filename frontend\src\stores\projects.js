import { defineStore } from 'pinia'
import { projectsAPI } from '@/services/api'

export const useProjectsStore = defineStore('projects', {
  state: () => ({
    projects: [],
    currentProject: null,
    loading: false,
    error: null
  }),

  getters: {
    getProjectById: (state) => (id) => {
      return state.projects.find(project => project.id === id)
    },

    projectsCount: (state) => state.projects.length,

    hasProjects: (state) => state.projects.length > 0
  },

  actions: {
    async fetchProjects(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await projectsAPI.getProjects(params)
        this.projects = response.projects || []
        return response
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async createProject(projectData) {
      this.loading = true
      this.error = null

      try {
        const response = await projectsAPI.createProject(projectData)
        this.projects.unshift(response)
        return response
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchProject(projectId) {
      this.loading = true
      this.error = null

      try {
        const project = await projectsAPI.getProject(projectId)
        this.currentProject = project
        
        // 更新項目列表中的項目
        const index = this.projects.findIndex(p => p.id === projectId)
        if (index !== -1) {
          this.projects[index] = project
        }
        
        return project
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateProject(projectId, updateData) {
      this.loading = true
      this.error = null

      try {
        const updatedProject = await projectsAPI.updateProject(projectId, updateData)
        
        // 更新項目列表
        const index = this.projects.findIndex(p => p.id === projectId)
        if (index !== -1) {
          this.projects[index] = updatedProject
        }
        
        // 更新當前項目
        if (this.currentProject && this.currentProject.id === projectId) {
          this.currentProject = updatedProject
        }
        
        return updatedProject
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async deleteProject(projectId) {
      this.loading = true
      this.error = null

      try {
        await projectsAPI.deleteProject(projectId)
        
        // 從項目列表中移除
        this.projects = this.projects.filter(p => p.id !== projectId)
        
        // 清除當前項目
        if (this.currentProject && this.currentProject.id === projectId) {
          this.currentProject = null
        }
        
        return true
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    setCurrentProject(project) {
      this.currentProject = project
    },

    clearCurrentProject() {
      this.currentProject = null
    },

    clearError() {
      this.error = null
    }
  }
})
