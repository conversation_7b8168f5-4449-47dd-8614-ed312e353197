#!/usr/bin/env python3
"""
测试修复后的多视频拼接功能
"""
import requests
import json
import time
import os
from moviepy.editor import VideoFileClip

def test_multi_video_concatenation():
    """测试多视频拼接功能"""
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    base_url = "http://localhost:8000"
    
    print("🔧 测试修复后的多视频拼接功能")
    print("=" * 50)
    
    # 1. 获取项目视频文件
    response = requests.get(f"{base_url}/api/projects/{project_id}/files")
    if response.status_code != 200:
        print(f"❌ 无法获取项目文件: {response.status_code}")
        return
    
    files = response.json()
    video_files = [f for f in files if f['file_type'] == 'video']
    
    print(f"📁 项目中有 {len(video_files)} 个视频文件")
    
    # 选择前3个视频进行测试
    selected_videos = [video_files[i]['filename'] for i in range(min(3, len(video_files)))]
    
    print(f"🎯 选择的视频文件:")
    total_expected_size = 0
    for i, filename in enumerate(selected_videos):
        file_info = next(f for f in video_files if f['filename'] == filename)
        size_mb = file_info['file_size'] / (1024 * 1024)
        total_expected_size += size_mb
        print(f"   {i+1}. {filename} ({size_mb:.2f} MB)")
    
    print(f"📊 预期总大小: {total_expected_size:.2f} MB")
    
    # 2. 构建编辑配置
    config = {
        "project_id": project_id,
        "middle_video_filenames": selected_videos,
        "output_size": "1080x1920",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"\n📝 编辑配置:")
    print(f"   片中视频数量: {len(config['middle_video_filenames'])}")
    print(f"   输出尺寸: {config['output_size']}")
    
    # 3. 启动编辑任务
    print(f"\n🚀 启动编辑任务...")
    response = requests.post(f"{base_url}/api/editing/start", json=config)
    
    if response.status_code != 200:
        print(f"❌ 启动失败: {response.status_code}")
        print(f"错误: {response.text}")
        return
    
    result = response.json()
    job_id = result.get("job_id")
    print(f"✅ 任务已启动，ID: {job_id}")
    
    # 4. 监控进度
    print(f"\n📊 监控进度...")
    start_time = time.time()
    
    for i in range(1, 61):  # 最多检查60次 (2分钟)
        response = requests.get(f"{base_url}/api/progress/{job_id}")
        if response.status_code != 200:
            time.sleep(2)
            continue
        
        data = response.json()
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        elapsed = time.time() - start_time
        
        print(f"   进度: {progress}% | 状态: {status} | 耗时: {elapsed:.1f}s")
        
        if status == "completed":
            output_path = data.get("output_path", "N/A")
            print(f"✅ 编辑完成!")
            print(f"📁 输出文件: {output_path}")
            
            # 5. 分析输出文件
            print(f"\n📊 分析输出文件...")
            full_output_path = f"backend/{output_path}"
            
            if os.path.exists(full_output_path):
                try:
                    clip = VideoFileClip(full_output_path)
                    actual_size = clip.size
                    actual_duration = clip.duration
                    actual_fps = clip.fps
                    clip.close()
                    
                    file_size = os.path.getsize(full_output_path) / (1024 * 1024)
                    
                    print(f"   文件大小: {file_size:.2f} MB")
                    print(f"   视频尺寸: {actual_size[0]}x{actual_size[1]}")
                    print(f"   视频时长: {actual_duration:.2f}s")
                    print(f"   帧率: {actual_fps:.2f} fps")
                    
                    # 分析结果
                    print(f"\n🔍 结果分析:")
                    
                    # 检查尺寸
                    expected_size = (1080, 1920)
                    if actual_size == expected_size:
                        print(f"   ✅ 尺寸正确: {actual_size[0]}x{actual_size[1]}")
                    else:
                        print(f"   ❌ 尺寸错误: 期望 {expected_size[0]}x{expected_size[1]}, 实际 {actual_size[0]}x{actual_size[1]}")
                    
                    # 检查时长 (多个视频应该有更长的时长)
                    if actual_duration > 10:  # 如果超过10秒，可能是多个视频
                        print(f"   ✅ 时长合理: {actual_duration:.2f}s (可能包含多个视频)")
                    elif actual_duration > 5:
                        print(f"   ⚠️ 时长中等: {actual_duration:.2f}s (可能只有部分视频)")
                    else:
                        print(f"   ❌ 时长太短: {actual_duration:.2f}s (可能只有一个视频)")
                    
                    # 检查文件大小
                    if file_size > 5:  # 如果超过5MB，可能是多个视频
                        print(f"   ✅ 文件大小合理: {file_size:.2f} MB")
                    else:
                        print(f"   ⚠️ 文件大小较小: {file_size:.2f} MB")
                    
                    return True
                    
                except Exception as e:
                    print(f"   ❌ 分析视频文件出错: {e}")
                    return False
            else:
                print(f"   ❌ 输出文件不存在: {full_output_path}")
                return False
            
        elif status == "failed":
            error = data.get("error", "Unknown error")
            print(f"❌ 编辑失败: {error}")
            return False
        
        if i < 60:
            time.sleep(2)
    
    print(f"⏰ 监控超时")
    return False

if __name__ == "__main__":
    success = test_multi_video_concatenation()
    
    if success:
        print(f"\n🎉 多视频拼接测试成功!")
    else:
        print(f"\n💥 多视频拼接测试失败")
