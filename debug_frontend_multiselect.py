#!/usr/bin/env python3
"""
檢查前端多選功能的問題
"""
import requests
import json

def check_frontend_multiselect():
    """檢查前端多選功能"""
    base_url = "http://localhost:8000"
    project_id = "e26a251a-0e00-4db7-a3fc-24b0341e2277"
    
    print("🔍 檢查前端多選功能問題")
    print("=" * 50)
    
    # 1. 檢查項目文件
    print("📁 檢查項目文件...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/files")
    if response.status_code != 200:
        print(f"❌ 無法獲取項目文件: {response.status_code}")
        return
    
    files = response.json()
    video_files = [f for f in files if f['file_type'] == 'video']
    
    print(f"📹 找到 {len(video_files)} 個視頻文件:")
    for i, file in enumerate(video_files[:5]):
        print(f"  {i+1}. {file['filename']}")
    
    # 2. 測試單個視頻（原始方式）
    print("\n🧪 測試1: 單個視頻（檢查向後兼容性）")
    config_single = {
        "project_id": project_id,
        "middle_video_filenames": [video_files[0]['filename']],  # 單個視頻
        "output_size": "1080x1920",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"📝 配置: {config_single['middle_video_filenames']}")
    
    response = requests.post(f"{base_url}/api/editing/start", json=config_single)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 單視頻測試成功，任務ID: {result.get('job_id')}")
    else:
        print(f"❌ 單視頻測試失敗: {response.status_code}")
        print(f"錯誤: {response.text}")
    
    # 3. 測試多個視頻
    print("\n🧪 測試2: 多個視頻（新功能）")
    config_multi = {
        "project_id": project_id,
        "middle_video_filenames": [
            video_files[0]['filename'],
            video_files[1]['filename']
        ],
        "output_size": "1080x1920", 
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0,
        "mute_original_audio": False,
        "enable_subtitles": False
    }
    
    print(f"📝 配置: {config_multi['middle_video_filenames']}")
    
    response = requests.post(f"{base_url}/api/editing/start", json=config_multi)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 多視頻測試成功，任務ID: {result.get('job_id')}")
    else:
        print(f"❌ 多視頻測試失敗: {response.status_code}")
        print(f"錯誤: {response.text}")
    
    # 4. 測試空數組（錯誤情況）
    print("\n🧪 測試3: 空視頻數組（錯誤處理）")
    config_empty = {
        "project_id": project_id,
        "middle_video_filenames": [],  # 空數組
        "output_size": "1080x1920",
        "subtitle_effect": "none",
        "background_volume": 0.3,
        "audio_volume": 1.0
    }
    
    response = requests.post(f"{base_url}/api/editing/start", json=config_empty)
    if response.status_code != 200:
        print(f"✅ 空數組正確被拒絕: {response.status_code}")
        print(f"錯誤信息: {response.text}")
    else:
        print(f"⚠️ 空數組意外被接受")
    
    # 5. 檢查前端配置結構
    print("\n🔍 檢查前端配置結構...")
    print("期望的前端配置結構:")
    print("""
    editingConfig: {
      intro_filename: '',
      intro_enabled: false,
      middle_video_filenames: [],  // 多選數組
      outro_filename: '',
      outro_enabled: false,
      // ... 其他配置
    }
    """)
    
    print("\n📋 前端多選組件應該是:")
    print("""
    <el-select
      v-model="editingConfig.middle_video_filenames"
      placeholder="選擇片中視頻（可多選）"
      multiple
      collapse-tags
      collapse-tags-tooltip
    >
      <el-option
        v-for="file in videoFiles"
        :key="file.id"
        :label="file.filename"
        :value="file.filename"
      />
    </el-select>
    """)

if __name__ == "__main__":
    check_frontend_multiselect()
